{% extends 'base.html' %}

{% block title %}Explore | PetPaw{% endblock %}

{% block extra_css %}
<style>
    .explore-container {
        padding: var(--spacing-xl) 0;
    }
    
    .explore-header {
        margin-bottom: var(--spacing-2xl);
        text-align: center;
    }
    
    .explore-title {
        font-size: var(--font-3xl);
        margin-bottom: var(--spacing-sm);
    }
    
    .explore-subtitle {
        color: var(--text-light);
        max-width: 600px;
        margin: 0 auto;
    }
    
    .explore-filters {
        display: flex;
        justify-content: center;
        gap: var(--gap-base);
        margin-bottom: var(--spacing-xl);
        flex-wrap: wrap;
    }
    
    .explore-filter {
        background-color: var(--gray-100);
        color: var(--text);
        padding: var(--spacing-sm) var(--spacing-base);
        border-radius: var(--radius-full);
        font-size: var(--font-sm);
        text-decoration: none;
        transition: var(--transition-base);
    }
    
    .explore-filter:hover, .explore-filter.active {
        background-color: var(--primary);
        color: var(--white);
    }
    
    .explore-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: var(--gap-base);
    }
    
    .explore-item {
        position: relative;
        border-radius: var(--radius-md);
        overflow: hidden;
        aspect-ratio: 1 / 1;
    }
    
    .explore-item img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: var(--transition-base);
    }
    
    .explore-item-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        opacity: 0;
        transition: var(--transition-base);
    }
    
    .explore-item:hover .explore-item-overlay {
        opacity: 1;
    }
    
    .explore-item:hover img {
        transform: scale(1.05);
    }
    
    .explore-item-stats {
        display: flex;
        gap: var(--gap-lg);
        color: var(--white);
        font-weight: var(--fw-medium);
    }
    
    .explore-item-stat {
        display: flex;
        align-items: center;
        gap: var(--gap-xs);
    }
    
    .explore-item-user {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: var(--spacing-base);
        background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
        display: flex;
        align-items: center;
        gap: var(--gap-sm);
        color: var(--white);
        opacity: 0;
        transition: var(--transition-base);
    }
    
    .explore-item:hover .explore-item-user {
        opacity: 1;
    }
    
    .explore-item-avatar {
        width: 30px;
        height: 30px;
        border-radius: var(--radius-full);
        object-fit: cover;
        border: 2px solid var(--white);
    }
    
    .explore-pagination {
        display: flex;
        justify-content: center;
        margin-top: var(--spacing-2xl);
    }
    
    .explore-pagination-list {
        display: flex;
        gap: var(--gap-xs);
        list-style: none;
        padding: 0;
    }
    
    .explore-pagination-item a {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border-radius: var(--radius-md);
        background-color: var(--white);
        color: var(--text);
        text-decoration: none;
        transition: var(--transition-base);
    }
    
    .explore-pagination-item a:hover, .explore-pagination-item.active a {
        background-color: var(--primary);
        color: var(--white);
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="explore-container">
        <div class="explore-header">
            <h1 class="explore-title">Explore</h1>
            <p class="explore-subtitle">Discover popular posts from the PetPaw community</p>
        </div>
        
        <div class="explore-filters">
            <a href="{% url 'explore' %}" class="explore-filter {% if not request.GET.hashtag %}active{% endif %}">All</a>
            {% for hashtag in popular_hashtags %}
                <a href="{% url 'explore' %}?hashtag={{ hashtag.name }}" class="explore-filter {% if request.GET.hashtag == hashtag.name %}active{% endif %}">#{{ hashtag.name }}</a>
            {% endfor %}
        </div>
        
        <div class="explore-grid">
            {% for post in posts %}
                <a href="{% url 'post-detail' pk=post.pk %}" class="explore-item">
                    {% if post.image %}
                        <img src="{{ post.image.url }}" alt="{{ post.content }}">
                    {% elif post.video %}
                        <img src="{{ post.get_video_thumbnail }}" alt="{{ post.content }}">
                        <div class="video-indicator">
                            <i class="fas fa-play"></i>
                        </div>
                    {% endif %}
                    
                    <div class="explore-item-overlay">
                        <div class="explore-item-stats">
                            <div class="explore-item-stat">
                                <i class="fas fa-heart"></i>
                                <span>{{ post.likes.count }}</span>
                            </div>
                            <div class="explore-item-stat">
                                <i class="fas fa-comment"></i>
                                <span>{{ post.comments.count }}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="explore-item-user">
                        <img src="{{ post.user.profile_picture.url }}" alt="{{ post.user.username }}" class="explore-item-avatar">
                        <span>{{ post.user.username }}</span>
                    </div>
                </a>
            {% empty %}
                <div class="empty-state">
                    <p>No posts found. Be the first to create a post!</p>
                    <a href="{% url 'post-create' %}" class="btn btn-primary">Create Post</a>
                </div>
            {% endfor %}
        </div>
        
        {% if is_paginated %}
            <div class="explore-pagination">
                <ul class="explore-pagination-list">
                    {% if page_obj.has_previous %}
                        <li class="explore-pagination-item">
                            <a href="?page=1{% if request.GET.hashtag %}&hashtag={{ request.GET.hashtag }}{% endif %}">
                                <i class="fas fa-angle-double-left"></i>
                            </a>
                        </li>
                        <li class="explore-pagination-item">
                            <a href="?page={{ page_obj.previous_page_number }}{% if request.GET.hashtag %}&hashtag={{ request.GET.hashtag }}{% endif %}">
                                <i class="fas fa-angle-left"></i>
                            </a>
                        </li>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <li class="explore-pagination-item active">
                                <a href="?page={{ num }}{% if request.GET.hashtag %}&hashtag={{ request.GET.hashtag }}{% endif %}">{{ num }}</a>
                            </li>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <li class="explore-pagination-item">
                                <a href="?page={{ num }}{% if request.GET.hashtag %}&hashtag={{ request.GET.hashtag }}{% endif %}">{{ num }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                        <li class="explore-pagination-item">
                            <a href="?page={{ page_obj.next_page_number }}{% if request.GET.hashtag %}&hashtag={{ request.GET.hashtag }}{% endif %}">
                                <i class="fas fa-angle-right"></i>
                            </a>
                        </li>
                        <li class="explore-pagination-item">
                            <a href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.hashtag %}&hashtag={{ request.GET.hashtag }}{% endif %}">
                                <i class="fas fa-angle-double-right"></i>
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
