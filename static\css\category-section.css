/* Category Section Styles */
.category-section {
  padding: var(--spacing-5xl) 0;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3xl);
  background-color: var(--secondary-50);
  position: relative;
  overflow: hidden;
  width: 100vw;
  margin-left: calc(-50vw + 50%);
  margin-right: calc(-50vw + 50%);
}

.category-section::before {
  content: '';
  position: absolute;
  top: -50px;
  right: -50px;
  width: 200px;
  height: 200px;
  border-radius: var(--radius-circle);
  background-color: var(--tertiary-50);
  opacity: 0.4;
  z-index: 0;
}

.category-section::after {
  content: '';
  position: absolute;
  bottom: -50px;
  left: -50px;
  width: 150px;
  height: 150px;
  border-radius: var(--radius-circle);
  background-color: var(--primary-50);
  opacity: 0.4;
  z-index: 0;
}

.category-section > * {
  position: relative;
  z-index: 1;
}

.category-text-container {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.category-text-container h2 {
  margin-bottom: var(--spacing-lg);
  color: var(--secondary);
  font-weight: var(--fw-bold);
}

.category-text-container p {
  color: var(--gray-700);
  max-width: 600px;
  margin: 0 auto;
}

.category-items-list-container {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: var(--spacing-2xl);
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  max-width: 1200px;
  width: 100%;
}

.category-list-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: var(--spacing-2xl);
  border-radius: var(--radius-6xl);
  background-color: var(--white);
  transition: var(--transition-base);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.category-list-item::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, var(--primary) 0%, var(--secondary) 100%);
  transform: scaleX(0);
  transform-origin: right;
  transition: transform 0.3s ease;
}

.category-list-item:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-lg);
  border-color: var(--secondary-100);
  text-decoration: none;
}

.category-list-item:hover::after {
  transform: scaleX(1);
  transform-origin: left;
}

.category-image-container {
  width: 120px;
  height: 120px;
  border-radius: var(--radius-circle);
  overflow: hidden;
  margin-bottom: var(--spacing-xl);
  border: 4px solid var(--secondary-100);
  transition: var(--transition-base);
  background-color: var(--white);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-sm);
}

.category-list-item:hover .category-image-container {
  border-color: var(--secondary);
  transform: scale(1.05);
  box-shadow: var(--shadow-md);
}

.category-image-container img {
  width: 60%;
  height: 60%;
  object-fit: contain;
  transition: var(--transition-base);
  stroke: var(--secondary);
  stroke-width: 2;
}

.category-list-item:hover .category-image-container img {
  stroke: var(--secondary-700);
  transform: scale(1.1);
}

.category-list-item h6 {
  color: var(--secondary);
  margin-bottom: var(--spacing-md);
  font-weight: var(--fw-semibold);
  transition: var(--transition-base);
}

.category-list-item p {
  color: var(--gray-600);
  font-size: var(--font-sm);
  margin-bottom: 0;
  transition: var(--transition-base);
}

.category-list-item:hover h6 {
  color: var(--secondary-700);
}

.category-list-item:hover p {
  color: var(--gray-800);
}

.category-action {
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-xl);
  padding: 0 var(--spacing-lg);
}

.category-action a.primary-lg-outline {
  border: var(--border-width-2) solid var(--secondary) !important;
  color: var(--secondary);
}

.category-action a.primary-lg-outline:hover {
  background-color: var(--secondary) !important;
  color: var(--white) !important;
}

/* Responsive styles */
@media (min-width: 576px) {
  .category-items-list-container {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 768px) {
  .category-items-list-container {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-3xl);
  }

  .category-text-container h2 {
    font-size: var(--font-h3);
  }
}

@media (min-width: 992px) {
  .category-items-list-container {
    grid-template-columns: repeat(4, 1fr);
  }

  .category-text-container h2 {
    font-size: var(--font-h2);
  }

  .category-section {
    padding: var(--spacing-6xl) 0;
  }
}

@media (min-width: 1200px) {
  .category-image-container {
    width: 150px;
    height: 150px;
  }

  .category-list-item h6 {
    font-size: var(--font-tl);
  }
}
