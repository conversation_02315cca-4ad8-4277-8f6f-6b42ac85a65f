from django.db import models
from django.urls import reverse
from users.models import User
from pets.models import Pet, PetCategory


class ServiceCategory(models.Model):
    """Model for service categories (grooming, walking, training, etc.)"""
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    icon = models.ImageField(upload_to='service_icons', blank=True)

    class Meta:
        verbose_name_plural = 'Service Categories'

    def __str__(self):
        return self.name


class ServiceProvider(models.Model):
    """Model for service providers"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='service_provider')
    profile_picture = models.ImageField(upload_to='provider_profiles', blank=True, null=True)
    categories = models.ManyToManyField(ServiceCategory, related_name='providers')
    pet_categories = models.ManyToManyField(PetCategory, related_name='service_providers')
    bio = models.TextField()
    experience_years = models.PositiveIntegerField(default=0)
    hourly_rate = models.DecimalField(max_digits=10, decimal_places=2)
    is_available = models.BooleanField(default=True)
    rating = models.DecimalField(max_digits=3, decimal_places=2, default=0)
    reviews_count = models.PositiveIntegerField(default=0)
    qualifications = models.TextField(blank=True)

    def __str__(self):
        return f"{self.user.username} - Service Provider"

    def get_absolute_url(self):
        return reverse('provider-detail', kwargs={'pk': self.pk})


class ServiceProviderGallery(models.Model):
    """Model for service provider gallery"""
    provider = models.ForeignKey(ServiceProvider, on_delete=models.CASCADE, related_name='gallery')
    image = models.ImageField(upload_to='provider_gallery')
    caption = models.CharField(max_length=255, blank=True)

    class Meta:
        verbose_name_plural = 'Service Provider Galleries'

    def __str__(self):
        return f"Image for {self.provider.user.username}"


class Service(models.Model):
    """Model for specific services offered by providers"""
    provider = models.ForeignKey(ServiceProvider, on_delete=models.CASCADE, related_name='services')
    category = models.ForeignKey(ServiceCategory, on_delete=models.CASCADE, related_name='services')
    name = models.CharField(max_length=200)
    description = models.TextField()
    price = models.DecimalField(max_digits=10, decimal_places=2)
    duration = models.PositiveIntegerField(help_text="Duration in minutes")
    is_available = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.name} by {self.provider.user.username}"

    def get_absolute_url(self):
        return reverse('service-detail', kwargs={'pk': self.pk})


class Availability(models.Model):
    """Model for service provider availability"""
    DAYS_OF_WEEK = (
        (0, 'Monday'),
        (1, 'Tuesday'),
        (2, 'Wednesday'),
        (3, 'Thursday'),
        (4, 'Friday'),
        (5, 'Saturday'),
        (6, 'Sunday'),
    )

    provider = models.ForeignKey(ServiceProvider, on_delete=models.CASCADE, related_name='availability')
    day_of_week = models.IntegerField(choices=DAYS_OF_WEEK)
    start_time = models.TimeField()
    end_time = models.TimeField()

    class Meta:
        verbose_name_plural = 'Availabilities'

    def __str__(self):
        return f"{self.provider.user.username} - {self.get_day_of_week_display()} ({self.start_time} - {self.end_time})"


class Booking(models.Model):
    """Model for service bookings"""
    STATUS_CHOICES = (
        ('pending', 'Pending'),
        ('confirmed', 'Confirmed'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    )

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='bookings')
    service = models.ForeignKey(Service, on_delete=models.CASCADE, related_name='bookings')
    pet = models.ForeignKey(Pet, on_delete=models.CASCADE, related_name='bookings')
    date = models.DateField()
    start_time = models.TimeField()
    end_time = models.TimeField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    total_price = models.DecimalField(max_digits=10, decimal_places=2)
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Booking for {self.service.name} by {self.user.username} on {self.date}"

    def get_absolute_url(self):
        return reverse('booking-detail', kwargs={'pk': self.pk})


class ServiceReview(models.Model):
    """Model for service reviews"""
    RATING_CHOICES = (
        (1, '1 - Poor'),
        (2, '2 - Fair'),
        (3, '3 - Good'),
        (4, '4 - Very Good'),
        (5, '5 - Excellent'),
    )

    booking = models.OneToOneField(Booking, on_delete=models.CASCADE, related_name='review')
    rating = models.IntegerField(choices=RATING_CHOICES)
    comment = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Review for {self.booking.service.name} by {self.booking.user.username}"

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)

        # Update provider's rating
        provider = self.booking.service.provider
        reviews = ServiceReview.objects.filter(booking__service__provider=provider)
        provider.rating = reviews.aggregate(models.Avg('rating'))['rating__avg'] or 0
        provider.reviews_count = reviews.count()
        provider.save()
