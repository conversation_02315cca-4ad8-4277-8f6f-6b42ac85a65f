{% extends 'base.html' %}

{% block title %}
    {% if form.instance.pk %}
        Edit {{ form.instance.name }} | PetPaw
    {% else %}
        Add a New Pet | PetPaw
    {% endif %}
{% endblock %}

{% block extra_css %}
<style>
    .pet-form-container {
        max-width: 800px;
        margin: 0 auto;
    }

    .pet-form-header {
        margin-bottom: var(--spacing-2xl);
    }

    .pet-form {
        background-color: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-base);
        padding: var(--spacing-2xl);
    }

    .form-section {
        margin-bottom: var(--spacing-2xl);
    }

    .form-section-title {
        font-size: var(--font-lg);
        margin-bottom: var(--spacing-lg);
        padding-bottom: var(--spacing-sm);
        border-bottom: 1px solid var(--gray-200);
    }

    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--gap-lg);
        margin-bottom: var(--spacing-lg);
    }

    @media (max-width: 768px) {
        .form-row {
            grid-template-columns: 1fr;
        }
    }

    .pet-picture-preview {
        width: 150px;
        height: 150px;
        border-radius: var(--radius-lg);
        object-fit: cover;
        margin-bottom: var(--spacing-base);
    }

    .form-actions {
        display: flex;
        justify-content: flex-end;
        gap: var(--spacing-base);
        margin-top: var(--spacing-2xl);
    }

    .adoption-toggle {
        margin-top: var(--spacing-lg);
    }

    .adoption-fields {
        margin-top: var(--spacing-lg);
        padding: var(--spacing-lg);
        background-color: var(--primary-light);
        border-radius: var(--radius-md);
        display: none;
    }

    .adoption-fields.show {
        display: block;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="pet-form-container">
        <div class="pet-form-header">
            <h1>
                {% if form.instance.pk %}
                    Edit {{ form.instance.name }}
                {% else %}
                    Add a New Pet
                {% endif %}
            </h1>
            <p>
                {% if form.instance.pk %}
                    Update your pet's information.
                {% else %}
                    Share your furry friend with the PetPaw community.
                {% endif %}
            </p>
        </div>

        <div class="pet-form">
            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}

                <div class="form-section">
                    <h2 class="form-section-title">Basic Information</h2>

                    <div class="form-group">
                        <label for="{{ form.name.id_for_label }}" class="form-label">Pet Name</label>
                        {{ form.name }}
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="{{ form.category.id_for_label }}" class="form-label">Category</label>
                            {{ form.category }}
                        </div>

                        <div class="form-group">
                            <label for="{{ form.breed.id_for_label }}" class="form-label">Breed</label>
                            {{ form.breed }}
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="{{ form.gender.id_for_label }}" class="form-label">Gender</label>
                            {{ form.gender }}
                        </div>

                        <div class="form-group">
                            <label for="{{ form.birth_date.id_for_label }}" class="form-label">Birth Date</label>
                            {{ form.birth_date }}
                            <small class="form-text">Approximate date is fine if you're not sure.</small>
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <h2 class="form-section-title">About Your Pet</h2>

                    <div class="form-group">
                        <label for="{{ form.bio.id_for_label }}" class="form-label">Bio</label>
                        {{ form.bio }}
                        <small class="form-text">Tell us about your pet's personality, likes, dislikes, etc.</small>
                    </div>
                </div>

                <div class="form-section">
                    <h2 class="form-section-title">Profile Picture</h2>

                    <div class="form-group">
                        {% if form.instance.profile_picture %}
                            <img src="{{ form.instance.profile_picture.url }}" alt="{{ form.instance.name }}" class="pet-picture-preview" id="pet-picture-preview">
                        {% else %}
                            <img src="/static/img/pet-placeholder.jpg" alt="Pet placeholder" class="pet-picture-preview" id="pet-picture-preview">
                        {% endif %}

                        <label for="{{ form.profile_picture.id_for_label }}" class="form-label">Upload Picture</label>
                        {{ form.profile_picture }}
                    </div>
                </div>

                <div class="form-section">
                    <h2 class="form-section-title">Adoption Status</h2>

                    <div class="form-group adoption-toggle">
                        <div class="form-check">
                            {{ form.is_for_adoption }}
                            <label for="{{ form.is_for_adoption.id_for_label }}" class="form-check-label">
                                This pet is available for adoption
                            </label>
                        </div>
                    </div>

                    <div class="adoption-fields" id="adoption-fields">
                        <div class="form-group">
                            <label for="{{ form.adoption_price.id_for_label }}" class="form-label">Adoption Price ($)</label>
                            {{ form.adoption_price }}
                            <small class="form-text">Leave blank if you're offering for free adoption.</small>
                        </div>
                    </div>
                </div>

                <div class="form-actions">
                    {% if form.instance.pk %}
                        <a href="{% url 'pet-detail' pk=form.instance.pk %}" class="btn btn-outline">Cancel</a>
                    {% else %}
                        <a href="{% url 'user-profile' username=user.username %}" class="btn btn-outline">Cancel</a>
                    {% endif %}
                    <button type="submit" class="btn btn-primary">
                        {% if form.instance.pk %}
                            Save Changes
                        {% else %}
                            Add Pet
                        {% endif %}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Preview pet picture before upload
        const petPictureInput = document.getElementById('{{ form.profile_picture.id_for_label }}');
        const petPicturePreview = document.getElementById('pet-picture-preview');

        if (petPictureInput && petPicturePreview) {
            petPictureInput.addEventListener('change', function() {
                if (this.files && this.files[0]) {
                    const reader = new FileReader();

                    reader.onload = function(e) {
                        petPicturePreview.src = e.target.result;
                    }

                    reader.readAsDataURL(this.files[0]);
                }
            });
        }

        // Show/hide adoption fields based on checkbox
        const adoptionCheckbox = document.getElementById('{{ form.is_for_adoption.id_for_label }}');
        const adoptionFields = document.getElementById('adoption-fields');

        if (adoptionCheckbox && adoptionFields) {
            // Initial state
            if (adoptionCheckbox.checked) {
                adoptionFields.classList.add('show');
            }

            // Toggle on change
            adoptionCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    adoptionFields.classList.add('show');
                } else {
                    adoptionFields.classList.remove('show');
                }
            });
        }

        // Dynamic breed loading based on category
        const categorySelect = document.getElementById('{{ form.category.id_for_label }}');
        const breedSelect = document.getElementById('{{ form.breed.id_for_label }}');

        if (categorySelect && breedSelect) {
            categorySelect.addEventListener('change', function() {
                const category = this.value;

                if (category) {
                    // Fetch breeds for selected category
                    fetch(`{% url 'ajax-load-breeds' %}?category=${category}`)
                        .then(response => response.json())
                        .then(data => {
                            // Clear current options
                            breedSelect.innerHTML = '<option value="">Select Breed</option>';

                            // Add new options
                            if (data.breeds && Array.isArray(data.breeds)) {
                                data.breeds.forEach(breed => {
                                    const option = document.createElement('option');
                                    option.value = breed.id;
                                    option.textContent = breed.name;
                                    breedSelect.appendChild(option);
                                });
                                console.log('Loaded breeds:', data.breeds);
                            } else {
                                console.error('Invalid response format:', data);
                            }
                        })
                        .catch(error => console.error('Error:', error));
                } else {
                    // Reset breed select if no category selected
                    breedSelect.innerHTML = '<option value="">Select Breed</option>';
                }
            });
        }
    });
</script>
{% endblock %}
