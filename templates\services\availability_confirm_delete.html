{% extends 'base.html' %}
{% load static %}

{% block title %}Delete Availability - PetPaw{% endblock %}

{% block content %}
<div class="container">
    <div class="confirm-delete-container">
        <div class="confirm-card">
            <div class="confirm-header">
                <div class="warning-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h1>Delete Availability Slot</h1>
                <p>Are you sure you want to delete this availability slot?</p>
            </div>

            <div class="availability-details">
                <h3>Availability Details</h3>
                <div class="detail-list">
                    <div class="detail-item">
                        <span class="label">Day:</span>
                        <span class="value">{{ availability.get_day_of_week_display }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="label">Time:</span>
                        <span class="value">{{ availability.start_time|time:"g:i A" }} - {{ availability.end_time|time:"g:i A" }}</span>
                    </div>
                </div>
            </div>

            <div class="warning-message">
                <div class="warning-content">
                    <i class="fas fa-info-circle"></i>
                    <div>
                        <strong>Warning:</strong> This action cannot be undone. Deleting this availability slot will remove it permanently from your schedule.
                    </div>
                </div>
            </div>

            <div class="confirm-actions">
                <a href="{% url 'provider-availability' %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i>
                    Cancel
                </a>
                <form method="post" style="display: inline;">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i>
                        Delete Availability
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.confirm-delete-container {
    max-width: 600px;
    margin: var(--spacing-4xl) auto;
    padding: var(--spacing-lg);
}

.confirm-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--gray-200);
    overflow: hidden;
}

.confirm-header {
    text-align: center;
    padding: var(--spacing-xl);
    background: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
}

.warning-icon {
    font-size: 3rem;
    color: var(--warning);
    margin-bottom: var(--spacing-base);
}

.confirm-header h1 {
    color: var(--gray-800);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-2xl);
}

.confirm-header p {
    color: var(--gray-600);
    font-size: var(--font-lg);
    margin: 0;
}

.availability-details {
    padding: var(--spacing-xl);
    border-bottom: 1px solid var(--gray-200);
}

.availability-details h3 {
    color: var(--gray-800);
    margin-bottom: var(--spacing-base);
    font-size: var(--font-lg);
}

.detail-list {
    display: grid;
    gap: var(--spacing-sm);
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--gray-100);
}

.detail-item:last-child {
    border-bottom: none;
}

.label {
    color: var(--gray-600);
    font-weight: 500;
}

.value {
    color: var(--gray-800);
    font-weight: 600;
}

.warning-message {
    padding: var(--spacing-xl);
    background: var(--warning-light);
    border-bottom: 1px solid var(--gray-200);
}

.warning-content {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
    color: var(--warning-dark);
}

.warning-content i {
    font-size: var(--font-lg);
    margin-top: 2px;
    flex-shrink: 0;
}

.confirm-actions {
    padding: var(--spacing-xl);
    background: var(--gray-50);
    display: flex;
    gap: var(--spacing-sm);
    justify-content: flex-end;
}

@media (max-width: 768px) {
    .confirm-delete-container {
        margin: var(--spacing-xl) auto;
        padding: var(--spacing-base);
    }
    
    .confirm-actions {
        flex-direction: column;
    }
    
    .confirm-actions .btn {
        width: 100%;
        justify-content: center;
    }
    
    .confirm-actions form {
        width: 100%;
    }
    
    .confirm-actions form .btn {
        width: 100%;
    }
}
</style>
{% endblock %}
