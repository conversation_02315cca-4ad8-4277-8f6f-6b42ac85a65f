* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: Arial, sans-serif;
}

body {
    background-color: #f5f5f5;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
}

.review-container {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    padding: 40px;
    width: 100%;
    max-width: 800px;
}

h1 {
    font-size: 28px;
    margin-bottom: 30px;
    color: #222;
}

h3 {
    font-size: 18px;
    margin-bottom: 15px;
    color: #333;
}

.business-info {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
}

.business-logo {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    object-fit: cover;
    margin-right: 20px;
}

.business-details h2 {
    font-size: 22px;
    margin-bottom: 5px;
    color: #333;
}

.business-details p {
    color: #666;
    font-size: 16px;
}

.rating-section {
    margin-bottom: 30px;
}

.star-rating {
    display: flex;
    gap: 10px;
}

.star {
    font-size: 32px;
    cursor: pointer;
    color: #ddd;
    transition: color 0.2s ease;
}

/* Star states with custom SVG icons */
.star {
    position: relative;
    width: 32px;
    height: 32px;
    cursor: pointer;
    transition: transform 0.2s ease;
}

/* Star rating container */
.star-rating {
    display: flex;
    flex-direction: row; /* Changed to left-to-right direction */
    justify-content: flex-start;
    gap: 5px;
}

/* Star icon positioning */
.star-icon {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    transition: all 0.2s ease;
}

/* Star states are now controlled by JavaScript */
/* We don't need CSS opacity rules anymore */

/* Scale effect on hover */
.star:hover {
    transform: scale(1.1);
}

.review-text-section {
    margin-bottom: 30px;
}

textarea {
    width: 100%;
    height: 150px;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    resize: vertical;
    font-size: 16px;
    transition: border-color 0.3s;
}

textarea:focus {
    outline: none;
    border-color: #0066cc;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
}

button {
    padding: 12px 24px;
    border-radius: 4px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s;
}

.btn-cancel {
    background-color: transparent;
    border: 1px solid #ddd;
    color: #666;
}

.btn-cancel:hover {
    background-color: #f5f5f5;
}

.btn-submit {
    background-color: #0066cc;
    border: none;
    color: white;
}

.btn-submit:hover {
    background-color: #0055aa;
}

/* This section is handled by the styles above */
