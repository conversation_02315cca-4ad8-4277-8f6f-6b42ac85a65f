document.addEventListener('DOMContentLoaded', function() {
    // Store ratings for each section
    const ratings = {
        'Proficiency': 0,
        'Responsiveness': 0,
        'Budget': 0,
        'Quality': 0
    };

    // Get all rating sections
    const ratingSections = document.querySelectorAll('.rating-section');

    // Initialize each rating section
    ratingSections.forEach(section => {
        const sectionTitle = section.querySelector('h3').textContent;
        const starRating = section.querySelector('.star-rating');
        const stars = section.querySelectorAll('.star');

        // Add event listeners to stars in this section
        stars.forEach(star => {
            const starValue = parseInt(star.getAttribute('data-rating'));

            // Hover effect
            star.addEventListener('mouseenter', function() {
                // Reset all stars to default state first
                stars.forEach(s => {
                    s.querySelector('.star-icon.outline').style.display = 'block';
                    s.querySelector('.star-icon.hover').style.display = 'none';
                    s.querySelector('.star-icon.selected').style.display = 'none';
                });

                // Get the current star's rating
                const currentRating = parseInt(this.getAttribute('data-rating'));

                stars.forEach(s => {
                    const starRating = parseInt(s.getAttribute('data-rating'));
                    if (starRating <= currentRating) {
                        s.querySelector('.star-icon.outline').style.display = 'none';
                        s.querySelector('.star-icon.hover').style.display = 'block';
                        s.querySelector('.star-icon.selected').style.display = 'none';
                    }
                });
            });

            // Click to select
            star.addEventListener('click', function() {
                // Reset all stars to default state first
                stars.forEach(s => {
                    s.querySelector('.star-icon.outline').style.display = 'block';
                    s.querySelector('.star-icon.hover').style.display = 'none';
                    s.querySelector('.star-icon.selected').style.display = 'none';
                });

                // Get the current star's rating
                const currentRating = parseInt(this.getAttribute('data-rating'));

                // Show selected state for this star and all stars before it
                stars.forEach(s => {
                    const starRating = parseInt(s.getAttribute('data-rating'));
                    if (starRating <= currentRating) {
                        s.querySelector('.star-icon.outline').style.display = 'none';
                        s.querySelector('.star-icon.hover').style.display = 'none';
                        s.querySelector('.star-icon.selected').style.display = 'block';
                    }
                });

                // Store the rating
                ratings[sectionTitle] = starValue;
            });
        });

        // Reset to current rating when mouse leaves the rating container
        starRating.addEventListener('mouseleave', function() {
            // Reset all stars to default state first
            stars.forEach(s => {
                s.querySelector('.star-icon.outline').style.display = 'block';
                s.querySelector('.star-icon.hover').style.display = 'none';
                s.querySelector('.star-icon.selected').style.display = 'none';
            });

            // If there's a selected rating, show it for all stars up to the rating
            if (ratings[sectionTitle] > 0) {
                const currentRating = ratings[sectionTitle];
                stars.forEach(s => {
                    const starRating = parseInt(s.getAttribute('data-rating'));
                    if (starRating <= currentRating) {
                        s.querySelector('.star-icon.outline').style.display = 'none';
                        s.querySelector('.star-icon.hover').style.display = 'none';
                        s.querySelector('.star-icon.selected').style.display = 'block';
                    }
                });
            }
        });
    });

    // Form submission
    const submitButton = document.querySelector('.btn-submit');
    submitButton.addEventListener('click', function(e) {
        e.preventDefault();

        const reviewText = document.querySelector('textarea').value;

        // Check if at least one rating is provided
        const hasRating = Object.values(ratings).some(rating => rating > 0);

        // Validate form
        if (!hasRating) {
            console.log('Please provide at least one rating');
            return;
        }

        // Here you would typically send the data to a server
        console.log('Ratings:', ratings);
        console.log('Review:', reviewText);

        console.log('Thank you for your review!');

        // Reset form
        Object.keys(ratings).forEach(key => {
            ratings[key] = 0;
        });

        // Reset all stars to default state
        document.querySelectorAll('.star').forEach(star => {
            star.querySelector('.star-icon.outline').style.display = 'block';
            star.querySelector('.star-icon.hover').style.display = 'none';
            star.querySelector('.star-icon.selected').style.display = 'none';
        });

        document.querySelector('textarea').value = '';
    });

    // Cancel button
    const cancelButton = document.querySelector('.btn-cancel');
    cancelButton.addEventListener('click', function() {
        // Reset all ratings
        Object.keys(ratings).forEach(key => {
            ratings[key] = 0;
        });

        // Reset all stars to default state
        document.querySelectorAll('.star').forEach(star => {
            star.querySelector('.star-icon.outline').style.display = 'block';
            star.querySelector('.star-icon.hover').style.display = 'none';
            star.querySelector('.star-icon.selected').style.display = 'none';
        });

        document.querySelector('textarea').value = '';
    });

    // Initialize all stars to show outline state only
    document.querySelectorAll('.star').forEach(star => {
        star.querySelector('.star-icon.outline').style.display = 'block';
        star.querySelector('.star-icon.hover').style.display = 'none';
        star.querySelector('.star-icon.selected').style.display = 'none';
    });
});
