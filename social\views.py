from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.contrib import messages
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy, reverse
from django.http import JsonResponse
from django.db.models import Q
from django.utils import timezone
from datetime import timedelta
import re
from .models import Post, Comment, Story, Notification, Hashtag
from .forms import PostForm, CommentForm, StoryForm
from users.models import User


class FeedView(LoginRequiredMixin, ListView):
    """View for displaying user's feed"""
    model = Post
    template_name = 'social/feed.html'
    context_object_name = 'posts'
    paginate_by = 10
    
    def get_queryset(self):
        # Get posts from users the current user follows
        following_users = self.request.user.following.values_list('user', flat=True)
        
        # Get posts from pets the current user follows
        followed_pets = self.request.user.followed_pets.values_list('id', flat=True)
        
        # Combine posts from followed users, followed pets, and the user's own posts
        queryset = Post.objects.filter(
            Q(user__in=following_users) | 
            Q(pet__in=followed_pets) | 
            Q(user=self.request.user)
        ).distinct().order_by('-created_at')
        
        return queryset
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Add active stories from followed users and own stories
        following_users = self.request.user.following.values_list('user', flat=True)
        now = timezone.now()
        
        stories = Story.objects.filter(
            Q(user__in=following_users) | 
            Q(user=self.request.user),
            expires_at__gt=now
        ).order_by('-created_at')
        
        context['stories'] = stories
        context['comment_form'] = CommentForm()
        
        # Add suggested users to follow
        # Exclude users the current user already follows and themselves
        following = self.request.user.following.values_list('user', flat=True)
        suggested_users = User.objects.exclude(
            Q(id__in=following) | 
            Q(id=self.request.user.id)
        ).order_by('?')[:5]  # Get 5 random users
        
        context['suggested_users'] = suggested_users
        
        return context


class ExploreView(ListView):
    """View for exploring public posts"""
    model = Post
    template_name = 'social/explore.html'
    context_object_name = 'posts'
    paginate_by = 12
    
    def get_queryset(self):
        # Get all public posts, ordered by popularity (likes count)
        queryset = Post.objects.annotate(
            like_count=models.Count('likes')
        ).order_by('-like_count', '-created_at')
        
        # Filter by hashtag if provided
        hashtag = self.request.GET.get('hashtag')
        if hashtag:
            queryset = queryset.filter(hashtags__name=hashtag)
        
        return queryset
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Add popular hashtags
        popular_hashtags = Hashtag.objects.annotate(
            post_count=models.Count('posts')
        ).order_by('-post_count')[:10]
        
        context['popular_hashtags'] = popular_hashtags
        context['current_hashtag'] = self.request.GET.get('hashtag')
        
        return context


class PostDetailView(DetailView):
    """View for displaying post details"""
    model = Post
    template_name = 'social/post_detail.html'
    context_object_name = 'post'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['comments'] = self.object.comments.all()
        context['comment_form'] = CommentForm()
        
        # Check if user has liked the post
        if self.request.user.is_authenticated:
            context['has_liked'] = self.object.likes.filter(id=self.request.user.id).exists()
        
        return context


@login_required
def create_post(request):
    """View for creating a new post"""
    if request.method == 'POST':
        form = PostForm(request.user, request.POST, request.FILES)
        if form.is_valid():
            post = form.save(commit=False)
            post.user = request.user
            post.save()
            
            # Extract and save hashtags
            content = form.cleaned_data.get('content', '')
            hashtag_pattern = r'#(\w+)'
            hashtags = re.findall(hashtag_pattern, content)
            
            for tag in hashtags:
                hashtag, created = Hashtag.objects.get_or_create(name=tag.lower())
                post.hashtags.add(hashtag)
            
            messages.success(request, 'Post created successfully!')
            return redirect('post-detail', pk=post.pk)
    else:
        form = PostForm(request.user)
    
    return render(request, 'social/post_form.html', {'form': form})


class PostUpdateView(LoginRequiredMixin, UserPassesTestMixin, UpdateView):
    """View for updating a post"""
    model = Post
    form_class = PostForm
    template_name = 'social/post_form.html'
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs
    
    def form_valid(self, form):
        response = super().form_valid(form)
        
        # Update hashtags
        self.object.hashtags.clear()
        content = form.cleaned_data.get('content', '')
        hashtag_pattern = r'#(\w+)'
        hashtags = re.findall(hashtag_pattern, content)
        
        for tag in hashtags:
            hashtag, created = Hashtag.objects.get_or_create(name=tag.lower())
            self.object.hashtags.add(hashtag)
        
        messages.success(self.request, 'Post updated successfully!')
        return response
    
    def test_func(self):
        post = self.get_object()
        return self.request.user == post.user


class PostDeleteView(LoginRequiredMixin, UserPassesTestMixin, DeleteView):
    """View for deleting a post"""
    model = Post
    template_name = 'social/post_confirm_delete.html'
    success_url = reverse_lazy('feed')
    
    def test_func(self):
        post = self.get_object()
        return self.request.user == post.user
    
    def delete(self, request, *args, **kwargs):
        messages.success(request, 'Post deleted successfully!')
        return super().delete(request, *args, **kwargs)


@login_required
def like_post(request, pk):
    """View for liking/unliking a post"""
    post = get_object_or_404(Post, pk=pk)
    
    if post.likes.filter(id=request.user.id).exists():
        post.likes.remove(request.user)
        liked = False
    else:
        post.likes.add(request.user)
        liked = True
        
        # Create notification for post owner if it's not the current user
        if post.user != request.user:
            Notification.objects.create(
                recipient=post.user,
                sender=request.user,
                notification_type='like',
                post=post,
                message=f"{request.user.username} liked your post."
            )
    
    # Handle AJAX requests
    if request.headers.get('x-requested-with') == 'XMLHttpRequest':
        return JsonResponse({
            'liked': liked,
            'like_count': post.get_like_count()
        })
    
    return redirect('post-detail', pk=pk)


@login_required
def add_comment(request, pk):
    """View for adding a comment to a post"""
    post = get_object_or_404(Post, pk=pk)
    
    if request.method == 'POST':
        form = CommentForm(request.POST)
        if form.is_valid():
            comment = form.save(commit=False)
            comment.post = post
            comment.user = request.user
            comment.save()
            
            # Create notification for post owner if it's not the current user
            if post.user != request.user:
                Notification.objects.create(
                    recipient=post.user,
                    sender=request.user,
                    notification_type='comment',
                    post=post,
                    comment=comment,
                    message=f"{request.user.username} commented on your post."
                )
            
            messages.success(request, 'Comment added successfully!')
    
    return redirect('post-detail', pk=pk)


@login_required
def create_story(request):
    """View for creating a new story"""
    if request.method == 'POST':
        form = StoryForm(request.user, request.POST, request.FILES)
        if form.is_valid():
            story = form.save(commit=False)
            story.user = request.user
            
            # Set expiration time (24 hours from now)
            story.expires_at = timezone.now() + timedelta(hours=24)
            
            story.save()
            messages.success(request, 'Story created successfully!')
            return redirect('feed')
    else:
        form = StoryForm(request.user)
    
    return render(request, 'social/story_form.html', {'form': form})


@login_required
def view_story(request, pk):
    """View for viewing a specific story"""
    story = get_object_or_404(Story, pk=pk)
    
    # Check if story is expired
    if story.is_expired():
        messages.error(request, 'This story has expired.')
        return redirect('feed')
    
    # Get next and previous stories
    following_users = request.user.following.values_list('user', flat=True)
    now = timezone.now()
    
    all_stories = Story.objects.filter(
        Q(user__in=following_users) | 
        Q(user=request.user),
        expires_at__gt=now
    ).order_by('-created_at')
    
    story_ids = list(all_stories.values_list('id', flat=True))
    current_index = story_ids.index(story.id) if story.id in story_ids else 0
    
    next_story_id = story_ids[current_index + 1] if current_index + 1 < len(story_ids) else None
    prev_story_id = story_ids[current_index - 1] if current_index > 0 else None
    
    return render(request, 'social/view_story.html', {
        'story': story,
        'next_story_id': next_story_id,
        'prev_story_id': prev_story_id
    })


@login_required
def notifications(request):
    """View for displaying user notifications"""
    notifications = Notification.objects.filter(recipient=request.user).order_by('-created_at')
    
    # Mark all as read
    unread_notifications = notifications.filter(is_read=False)
    unread_notifications.update(is_read=True)
    
    return render(request, 'social/notifications.html', {'notifications': notifications})
