/* Testimonials Section Styles */
.testimonials-section {
  padding: var(--spacing-5xl) 0;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3xl);
  background-color: var(--tertiary-50);
  background-image: url('./img/paw-pattern.svg');
  background-repeat: repeat;
  background-size: 60px;
  position: relative;
  overflow: hidden;
  width: 100vw;
  margin-left: calc(-50vw + 50%);
  margin-right: calc(-50vw + 50%);
}

.testimonials-section::before {
  content: '';
  position: absolute;
  top: -50px;
  right: -50px;
  width: 200px;
  height: 200px;
  border-radius: var(--radius-circle);
  background-color: var(--tertiary-100);
  opacity: 0.4;
  z-index: 0;
}

.testimonials-section::after {
  content: '';
  position: absolute;
  bottom: -50px;
  left: -50px;
  width: 150px;
  height: 150px;
  border-radius: var(--radius-circle);
  background-color: var(--tertiary-100);
  opacity: 0.4;
  z-index: 0;
}

.testimonials-section > * {
  position: relative;
  z-index: 1;
}

.testimonial-section-text-container {
  text-align: center;
  max-width: 800px;
  margin: 0 auto var(--spacing-3xl);
  padding: 0 var(--spacing-lg);
}

.testimonial-section-text-container h2 {
  margin-bottom: var(--spacing-lg);
  color: var(--tertiary);
  font-weight: var(--fw-bold);
  position: relative;
  display: inline-block;
  padding-bottom: var(--spacing-md);
}

.testimonial-section-text-container h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background-color: var(--tertiary);
}

.testimonial-section-text-container p {
  color: var(--gray-700);
  max-width: 600px;
  margin: var(--spacing-lg) auto 0;
  line-height: 1.6;
}

.testimonial-list-container {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: var(--spacing-2xl);
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  max-width: 1200px;
  width: 100%;
}

.testimonial-card {
  background-color: var(--white);
  border-radius: var(--radius-6xl);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-3xl) var(--spacing-2xl) var(--spacing-2xl);
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  border: 1px solid var(--gray-200);
  transition: var(--transition-base);
  height: 100%;
  text-align: left;
}

.testimonial-card::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, var(--tertiary) 0%, var(--tertiary-300) 100%);
  transform: scaleX(0);
  transform-origin: right;
  transition: transform 0.3s ease;
}

.testimonial-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-lg);
  border-color: var(--tertiary-100);
}

.testimonial-card:hover::after {
  transform: scaleX(1);
  transform-origin: left;
}

.testimonial-quote-icon {
  margin-bottom: var(--spacing-lg);
  position: absolute;
  top: var(--spacing-xl);
  left: var(--spacing-xl);
  background-color: var(--tertiary-50);
  width: 40px;
  height: 40px;
  border-radius: var(--radius-circle);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-base);
  z-index: 2;
}

.testimonial-quote-icon img {
  width: 24px;
  height: 24px;
  stroke: var(--tertiary);
  stroke-width: 2;
  transition: var(--transition-base);
}

.testimonial-card:hover .testimonial-quote-icon {
  background-color: var(--tertiary-100);
  transform: rotate(-10deg);
}

.testimonial-card:hover .testimonial-quote-icon img {
  stroke: var(--tertiary-700);
}

.testimonial-content {
  margin-bottom: var(--spacing-xl);
  padding-top: var(--spacing-lg);
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.testimonial-text {
  font-size: var(--font-lg);
  line-height: 1.6;
  color: var(--gray-700);
  font-style: italic;
  position: relative;
  margin: 0;
  padding-left: var(--spacing-lg);
  padding-right: var(--spacing-sm);
}

.testimonial-text::first-letter {
  font-size: 1.2em;
  font-weight: var(--fw-semibold);
  color: var(--tertiary);
}

.testimonial-author {
  display: flex;
  align-items: center;
  margin-top: auto;
  padding-top: var(--spacing-lg);
  padding-left: var(--spacing-lg);
  padding-right: var(--spacing-lg);
  border-top: 1px solid var(--gray-200);
  position: relative;
}

.testimonial-author::before {
  content: '';
  position: absolute;
  top: -1px;
  left: var(--spacing-lg);
  width: 50px;
  height: 2px;
  background-color: var(--tertiary);
  transition: var(--transition-base);
}

.testimonial-card:hover .testimonial-author::before {
  width: 80px;
}

.testimonial-author img {
  width: 50px;
  height: 50px;
  border-radius: var(--radius-circle);
  margin-right: var(--spacing-lg);
  border: 2px solid var(--tertiary-100);
  transition: var(--transition-base);
  object-fit: cover;
}

.testimonial-card:hover .testimonial-author img {
  border-color: var(--tertiary);
  transform: scale(1.05);
  box-shadow: var(--shadow-sm);
}

.author-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.author-name {
  font-weight: var(--fw-semibold);
  color: var(--tertiary);
  font-size: var(--font-lg);
  margin-bottom: var(--spacing-xm);
  transition: var(--transition-base);
  line-height: 1.2;
}

.testimonial-card:hover .author-name {
  color: var(--tertiary-700);
}

.author-title {
  color: var(--gray-600);
  font-size: var(--font-sm);
  transition: var(--transition-base);
  line-height: 1.2;
  margin: 0;
}

.testimonial-card:hover .author-title {
  color: var(--gray-800);
}

.testimonial-action {
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-3xl);
  padding: 0 var(--spacing-lg);
}

/* Custom button style for testimonials section */
.tertiary-lg-outline {
  font-family: var(--font-family-sans);
  font-size: var(--font-lg);
  font-weight: var(--fw-semibold);
  line-height: var(--line-height-lg);
  color: var(--tertiary);
  padding: calc(var(--spacing-r) - var(--border-width-2)) var(--spacing-xl);
  border-radius: var(--radius-4xl);
  border: var(--border-width-2) solid var(--tertiary) !important;
  cursor: pointer;
  transition: var(--transition-base);
  text-align: center;
  display: inline-block;
  text-decoration: none;
}

.tertiary-lg-outline:hover {
  background-color: var(--tertiary) !important;
  color: var(--white) !important;
  text-decoration: none;
}

/* Responsive styles */
@media (min-width: 576px) {
  .testimonial-list-container {
    grid-template-columns: repeat(1, 1fr);
    max-width: 500px;
  }
}

@media (min-width: 768px) {
  .testimonial-list-container {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-3xl);
    max-width: 100%;
  }

  .testimonial-section-text-container h2 {
    font-size: var(--font-h3);
  }

  .testimonial-card {
    min-height: 320px;
  }
}

@media (min-width: 992px) {
  .testimonial-list-container {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-2xl);
  }

  .testimonial-section-text-container h2 {
    font-size: var(--font-h2);
  }

  .testimonials-section {
    padding: var(--spacing-6xl) 0;
  }

  .testimonial-card {
    min-height: 320px;
    max-height: 320px;
  }

  .testimonial-content {
    height: 180px;
    overflow: hidden;
  }
}

@media (min-width: 1200px) {
  .testimonial-quote-icon {
    width: 48px;
    height: 48px;
  }

  .testimonial-quote-icon img {
    width: 24px;
    height: 24px;
  }

  .testimonial-author img {
    width: 56px;
    height: 56px;
  }

  .testimonial-card {
    min-height: 320px;
    max-height: 320px;
  }

  .testimonial-content {
    height: 180px;
  }

  .testimonial-list-container {
    max-width: 1100px;
    margin: 0 auto;
  }
}

@media (max-width: 767px) {
  .testimonial-card {
    max-width: 100%;
    width: 100%;
    margin-bottom: 0;
    min-height: auto;
    padding: var(--spacing-2xl) var(--spacing-lg) var(--spacing-lg);
  }

  .testimonial-action a {
    display: block;
    margin: var(--spacing-lg) auto;
    max-width: 200px;
  }

  .testimonial-list-container {
    gap: var(--spacing-xl);
  }

  .testimonial-quote-icon {
    top: var(--spacing-lg);
    left: var(--spacing-lg);
    width: 36px;
    height: 36px;
  }

  .testimonial-quote-icon img {
    width: 18px;
    height: 18px;
  }

  .testimonial-content {
    padding-top: var(--spacing-lg);
    height: auto;
    margin-bottom: var(--spacing-lg);
  }

  .testimonial-text {
    font-size: 0.95em;
    padding-left: var(--spacing-md);
    padding-right: 0;
  }

  .testimonial-author {
    padding-left: var(--spacing-md);
    padding-right: var(--spacing-md);
  }

  .testimonial-author::before {
    left: var(--spacing-md);
  }

  .author-name {
    font-size: 1em;
  }
}
