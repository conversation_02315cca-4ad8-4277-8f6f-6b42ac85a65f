{% extends 'base.html' %}

{% block title %}Delete Post | PetPaw{% endblock %}

{% block extra_css %}
<style>
    .delete-container {
        max-width: 600px;
        margin: 0 auto;
        padding: var(--spacing-xl) 0;
    }
    
    .delete-card {
        background-color: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-base);
        padding: var(--spacing-2xl);
        text-align: center;
    }
    
    .delete-icon {
        font-size: var(--font-4xl);
        color: var(--danger);
        margin-bottom: var(--spacing-lg);
    }
    
    .delete-title {
        font-size: var(--font-2xl);
        margin-bottom: var(--spacing-lg);
    }
    
    .delete-message {
        margin-bottom: var(--spacing-xl);
        color: var(--text-light);
    }
    
    .post-preview {
        margin: var(--spacing-xl) 0;
        padding: var(--spacing-lg);
        border: 1px solid var(--gray-200);
        border-radius: var(--radius-md);
        text-align: left;
    }
    
    .post-preview-header {
        display: flex;
        align-items: center;
        gap: var(--gap-base);
        margin-bottom: var(--spacing-base);
    }
    
    .post-preview-avatar {
        width: 40px;
        height: 40px;
        border-radius: var(--radius-full);
        object-fit: cover;
    }
    
    .post-preview-content {
        margin-bottom: var(--spacing-base);
    }
    
    .post-preview-image {
        max-width: 100%;
        max-height: 200px;
        object-fit: cover;
        border-radius: var(--radius-md);
        margin-bottom: var(--spacing-base);
    }
    
    .delete-actions {
        display: flex;
        justify-content: center;
        gap: var(--gap-lg);
        margin-top: var(--spacing-xl);
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="delete-container">
        <div class="delete-card">
            <div class="delete-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            
            <h1 class="delete-title">Delete Post</h1>
            
            <p class="delete-message">
                Are you sure you want to delete this post? This action cannot be undone.
            </p>
            
            <div class="post-preview">
                <div class="post-preview-header">
                    <img src="{{ post.user.profile_picture.url }}" alt="{{ post.user.username }}" class="post-preview-avatar">
                    <div>
                        <div class="post-preview-username">{{ post.user.username }}</div>
                        <div class="post-preview-time">{{ post.created_at|timesince }} ago</div>
                    </div>
                </div>
                
                <div class="post-preview-content">
                    {{ post.content|truncatechars:100 }}
                </div>
                
                {% if post.image %}
                    <img src="{{ post.image.url }}" alt="{{ post.content }}" class="post-preview-image">
                {% endif %}
                
                {% if post.video %}
                    <video src="{{ post.video.url }}" controls class="post-preview-image"></video>
                {% endif %}
            </div>
            
            <form method="post">
                {% csrf_token %}
                <div class="delete-actions">
                    <a href="{% url 'post-detail' pk=post.pk %}" class="btn btn-outline">Cancel</a>
                    <button type="submit" class="btn btn-danger">Delete Post</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
