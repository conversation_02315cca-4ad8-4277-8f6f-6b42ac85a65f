{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}
    {% if form.instance.id %}Edit Service{% else %}Add New Service{% endif %} - PetPaw
{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">{% if form.instance.id %}Edit Service{% else %}Add New Service{% endif %}</h5>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                {{ form.category|as_crispy_field }}
                            </div>
                            <div class="col-md-6">
                                {{ form.name|as_crispy_field }}
                            </div>
                        </div>
                        
                        {{ form.description|as_crispy_field }}
                        
                        <div class="row">
                            <div class="col-md-6">
                                {{ form.price|as_crispy_field }}
                            </div>
                            <div class="col-md-6">
                                {{ form.duration|as_crispy_field }}
                            </div>
                        </div>
                        
                        {{ form.is_available|as_crispy_field }}
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                            <a href="{% url 'provider-services' %}" class="btn btn-outline-secondary me-md-2">
                                <i class="fas fa-times me-2"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i> {% if form.instance.id %}Update{% else %}Create{% endif %} Service
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
