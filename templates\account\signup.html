{% extends "base.html" %}

{% load i18n %}
{% load account socialaccount %}

{% block title %}{% trans "Sign Up" %} | PetPaw{% endblock %}

{% block extra_css %}
<style>
    /* SAME STYLES AS LOGIN PAGE - already provided */
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <h1 class="auth-title">{% trans "Sign Up" %}</h1>
                <p class="auth-subtitle">{% trans "Join PetPaw and connect with pet lovers" %}</p>
            </div>

            <form class="auth-form" method="post" action="{% url 'account_signup' %}">
                {% csrf_token %}

                {% if form.non_field_errors %}
                    <div class="alert alert-danger">
                        {% for error in form.non_field_errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}

                <div class="form-group">
                    <label for="{{ form.username.id_for_label }}" class="form-label">{% trans "Username" %}</label>
                    {{ form.username }}
                    {% if form.username.errors %}
                        <ul class="errorlist">
                            {% for error in form.username.errors %}
                                <li>{{ error }}</li>
                            {% endfor %}
                        </ul>
                    {% endif %}
                </div>

                <div class="form-group">
                    <label for="{{ form.email.id_for_label }}" class="form-label">{% trans "Email" %}</label>
                    {{ form.email }}
                    {% if form.email.errors %}
                        <ul class="errorlist">
                            {% for error in form.email.errors %}
                                <li>{{ error }}</li>
                            {% endfor %}
                        </ul>
                    {% endif %}
                    <small class="form-text">{% trans "We'll never share your email with anyone else." %}</small>
                </div>

                <div class="form-group">
                    <label for="{{ form.password1.id_for_label }}" class="form-label">{% trans "Password" %}</label>
                    {{ form.password1 }}
                    {% if form.password1.errors %}
                        <ul class="errorlist">
                            {% for error in form.password1.errors %}
                                <li>{{ error }}</li>
                            {% endfor %}
                        </ul>
                    {% endif %}
                </div>

                <div class="form-group">
                    <label for="{{ form.password2.id_for_label }}" class="form-label">{% trans "Confirm Password" %}</label>
                    {{ form.password2 }}
                    {% if form.password2.errors %}
                        <ul class="errorlist">
                            {% for error in form.password2.errors %}
                                <li>{{ error }}</li>
                            {% endfor %}
                        </ul>
                    {% endif %}
                </div>

                {% if redirect_field_value %}
                    <input type="hidden" name="{{ redirect_field_name }}" value="{{ redirect_field_value }}" />
                {% endif %}

                <button type="submit" class="btn btn-primary auth-submit">{% trans "Sign Up" %}</button>
            </form>

            {% get_providers as socialaccount_providers %}
            {% if socialaccount_providers %}
            <div class="auth-divider">
                <div class="auth-divider-line"></div>
                <div class="auth-divider-text">{% trans "or sign up with" %}</div>
                <div class="auth-divider-line"></div>
            </div>

            <div class="social-login">
                {% if 'google' in socialaccount_providers %}
                <a href="{% provider_login_url 'google' %}" class="social-button">
                    <i class="fab fa-google social-icon"></i>
                    <span>{% trans "Continue with Google" %}</span>
                </a>
                {% endif %}

                {% if 'facebook' in socialaccount_providers %}
                <a href="{% provider_login_url 'facebook' %}" class="social-button">
                    <i class="fab fa-facebook social-icon"></i>
                    <span>{% trans "Continue with Facebook" %}</span>
                </a>
                {% endif %}
            </div>
            {% endif %}

            <div class="auth-footer">
                <p>{% trans "Already have an account?" %} <a href="{% url 'account_login' %}">{% trans "Log In" %}</a></p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
