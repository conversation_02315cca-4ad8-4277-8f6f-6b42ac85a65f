{% extends 'base.html' %}

{% block title %}Messages | PetPaw{% endblock %}

{% block extra_css %}
<style>
    .messaging-container {
        display: grid;
        grid-template-columns: 350px 1fr;
        height: calc(100vh - 200px);
        min-height: 600px;
        background-color: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-base);
        overflow: hidden;
    }
    
    @media (max-width: 992px) {
        .messaging-container {
            grid-template-columns: 1fr;
        }
    }
    
    .conversation-list {
        border-right: 1px solid var(--gray-200);
        overflow-y: auto;
    }
    
    .conversation-list-header {
        padding: var(--spacing-base) var(--spacing-xl);
        border-bottom: 1px solid var(--gray-200);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    
    .conversation-list-title {
        font-size: var(--font-lg);
        margin: 0;
    }
    
    .new-message-button {
        background: none;
        border: none;
        color: var(--primary);
        cursor: pointer;
        font-size: var(--font-xl);
    }
    
    .search-conversations {
        padding: var(--spacing-base) var(--spacing-xl);
        border-bottom: 1px solid var(--gray-200);
    }
    
    .search-input {
        width: 100%;
        padding: var(--spacing-sm) var(--spacing-base);
        border: 1px solid var(--gray-300);
        border-radius: var(--radius-full);
        font-size: var(--font-sm);
    }
    
    .search-input:focus {
        outline: none;
        border-color: var(--primary);
    }
    
    .conversation-items {
        padding: var(--spacing-base) 0;
    }
    
    .conversation-item {
        display: flex;
        align-items: center;
        gap: var(--gap-base);
        padding: var(--spacing-base) var(--spacing-xl);
        cursor: pointer;
        transition: var(--transition-base);
    }
    
    .conversation-item:hover {
        background-color: var(--gray-100);
    }
    
    .conversation-item.active {
        background-color: var(--primary-light);
    }
    
    .conversation-avatar {
        width: 50px;
        height: 50px;
        border-radius: var(--radius-full);
        object-fit: cover;
    }
    
    .conversation-info {
        flex: 1;
        min-width: 0;
    }
    
    .conversation-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-xs);
    }
    
    .conversation-name {
        font-weight: var(--fw-medium);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
    .conversation-time {
        font-size: var(--font-xs);
        color: var(--text-light);
        white-space: nowrap;
    }
    
    .conversation-preview {
        display: flex;
        align-items: center;
        gap: var(--gap-xs);
        color: var(--text-light);
        font-size: var(--font-sm);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
    .conversation-status {
        position: relative;
    }
    
    .unread-badge {
        position: absolute;
        top: -5px;
        right: -5px;
        background-color: var(--primary);
        color: var(--white);
        font-size: 10px;
        width: 18px;
        height: 18px;
        border-radius: var(--radius-full);
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .empty-conversation {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        padding: var(--spacing-xl);
        text-align: center;
        color: var(--text-light);
    }
    
    .empty-conversation-icon {
        font-size: 4rem;
        color: var(--gray-300);
        margin-bottom: var(--spacing-xl);
    }
    
    .empty-conversation-message {
        font-size: var(--font-lg);
        margin-bottom: var(--spacing-base);
    }
    
    .new-message-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: var(--z-50);
        opacity: 0;
        visibility: hidden;
        transition: var(--transition-base);
    }
    
    .new-message-modal.show {
        opacity: 1;
        visibility: visible;
    }
    
    .modal-content {
        background-color: var(--white);
        border-radius: var(--radius-lg);
        width: 100%;
        max-width: 500px;
        box-shadow: var(--shadow-xl);
    }
    
    .modal-header {
        padding: var(--spacing-base) var(--spacing-xl);
        border-bottom: 1px solid var(--gray-200);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    
    .modal-title {
        font-size: var(--font-lg);
        margin: 0;
    }
    
    .modal-close {
        background: none;
        border: none;
        font-size: var(--font-xl);
        cursor: pointer;
        color: var(--text-light);
    }
    
    .modal-body {
        padding: var(--spacing-xl);
    }
    
    .modal-footer {
        padding: var(--spacing-base) var(--spacing-xl);
        border-top: 1px solid var(--gray-200);
        display: flex;
        justify-content: flex-end;
        gap: var(--gap-base);
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="messaging-container">
        <div class="conversation-list">
            <div class="conversation-list-header">
                <h2 class="conversation-list-title">Messages</h2>
                <button type="button" class="new-message-button" id="new-message-btn">
                    <i class="fas fa-edit"></i>
                </button>
            </div>
            
            <div class="search-conversations">
                <input type="text" placeholder="Search conversations..." class="search-input" id="search-conversations">
            </div>
            
            <div class="conversation-items">
                {% for conversation in conversations %}
                    <a href="{% url 'conversation-detail' pk=conversation.id %}" class="conversation-item {% if active_conversation and active_conversation.id == conversation.id %}active{% endif %}">
                        {% if conversation.other_user == user %}
                            <img src="{{ conversation.creator.profile_picture.url }}" alt="{{ conversation.creator.username }}" class="conversation-avatar">
                        {% else %}
                            <img src="{{ conversation.other_user.profile_picture.url }}" alt="{{ conversation.other_user.username }}" class="conversation-avatar">
                        {% endif %}
                        
                        <div class="conversation-info">
                            <div class="conversation-header">
                                <div class="conversation-name">
                                    {% if conversation.other_user == user %}
                                        {{ conversation.creator.username }}
                                    {% else %}
                                        {{ conversation.other_user.username }}
                                    {% endif %}
                                </div>
                                <div class="conversation-time">{{ conversation.last_message.created_at|date:"g:i A" }}</div>
                            </div>
                            
                            <div class="conversation-preview">
                                {% if conversation.last_message.sender == user %}
                                    <span>You:</span>
                                {% endif %}
                                {{ conversation.last_message.content|truncatechars:30 }}
                            </div>
                        </div>
                        
                        <div class="conversation-status">
                            {% if conversation.unread_count > 0 and conversation.last_message.sender != user %}
                                <div class="unread-badge">{{ conversation.unread_count }}</div>
                            {% endif %}
                        </div>
                    </a>
                {% empty %}
                    <div class="empty-state">
                        <p>No conversations yet.</p>
                        <button type="button" class="btn btn-primary" id="empty-new-message-btn">Start a Conversation</button>
                    </div>
                {% endfor %}
            </div>
        </div>
        
        <div class="conversation-content">
            {% if active_conversation %}
                <!-- Conversation content will be loaded via AJAX -->
                <div id="conversation-detail-container"></div>
            {% else %}
                <div class="empty-conversation">
                    <div class="empty-conversation-icon">
                        <i class="far fa-comments"></i>
                    </div>
                    <h3 class="empty-conversation-message">Select a conversation to start messaging</h3>
                    <p>Or start a new conversation by clicking the new message button.</p>
                </div>
            {% endif %}
        </div>
    </div>
    
    <!-- New Message Modal -->
    <div class="new-message-modal" id="new-message-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">New Message</h3>
                <button type="button" class="modal-close" id="modal-close-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div class="modal-body">
                <form id="new-conversation-form">
                    <div class="form-group">
                        <label for="recipient" class="form-label">To:</label>
                        <input type="text" id="recipient" name="recipient" class="form-control" placeholder="Enter username" required>
                        <div id="recipient-suggestions" class="dropdown-menu"></div>
                    </div>
                    
                    <div class="form-group">
                        <label for="message" class="form-label">Message:</label>
                        <textarea id="message" name="message" rows="4" class="form-control" placeholder="Type your message here..." required></textarea>
                    </div>
                </form>
            </div>
            
            <div class="modal-footer">
                <button type="button" class="btn btn-outline" id="modal-cancel-btn">Cancel</button>
                <button type="button" class="btn btn-primary" id="send-message-btn">Send Message</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Load active conversation if exists
        {% if active_conversation %}
            loadConversation({{ active_conversation.id }});
        {% endif %}
        
        // New Message Modal
        const newMessageBtn = document.getElementById('new-message-btn');
        const emptyNewMessageBtn = document.getElementById('empty-new-message-btn');
        const newMessageModal = document.getElementById('new-message-modal');
        const modalCloseBtn = document.getElementById('modal-close-btn');
        const modalCancelBtn = document.getElementById('modal-cancel-btn');
        const sendMessageBtn = document.getElementById('send-message-btn');
        
        function openModal() {
            newMessageModal.classList.add('show');
        }
        
        function closeModal() {
            newMessageModal.classList.remove('show');
            document.getElementById('new-conversation-form').reset();
        }
        
        if (newMessageBtn) {
            newMessageBtn.addEventListener('click', openModal);
        }
        
        if (emptyNewMessageBtn) {
            emptyNewMessageBtn.addEventListener('click', openModal);
        }
        
        if (modalCloseBtn) {
            modalCloseBtn.addEventListener('click', closeModal);
        }
        
        if (modalCancelBtn) {
            modalCancelBtn.addEventListener('click', closeModal);
        }
        
        // Close modal when clicking outside
        newMessageModal.addEventListener('click', function(e) {
            if (e.target === newMessageModal) {
                closeModal();
            }
        });
        
        // Send new message
        if (sendMessageBtn) {
            sendMessageBtn.addEventListener('click', function() {
                const recipient = document.getElementById('recipient').value;
                const message = document.getElementById('message').value;
                
                if (!recipient || !message) {
                    return;
                }
                
                // Send message via AJAX
                fetch(`{% url 'start-conversation' username='placeholder' %}`.replace('placeholder', recipient), {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': getCookie('csrftoken'),
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        message: message
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Redirect to the new conversation
                        window.location.href = data.redirect_url;
                    } else {
                        // Show error message
                        alert(data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
            });
        }
        
        // Search conversations
        const searchInput = document.getElementById('search-conversations');
        const conversationItems = document.querySelectorAll('.conversation-item');
        
        if (searchInput && conversationItems.length) {
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                
                conversationItems.forEach(item => {
                    const name = item.querySelector('.conversation-name').textContent.toLowerCase();
                    const preview = item.querySelector('.conversation-preview').textContent.toLowerCase();
                    
                    if (name.includes(searchTerm) || preview.includes(searchTerm)) {
                        item.style.display = '';
                    } else {
                        item.style.display = 'none';
                    }
                });
            });
        }
        
        // Load conversation
        function loadConversation(conversationId) {
            fetch(`{% url 'load-messages' pk=0 %}`.replace('0', conversationId))
                .then(response => response.text())
                .then(html => {
                    document.getElementById('conversation-detail-container').innerHTML = html;
                    
                    // Scroll to bottom of messages
                    const messagesContainer = document.querySelector('.messages-container');
                    if (messagesContainer) {
                        messagesContainer.scrollTop = messagesContainer.scrollHeight;
                    }
                    
                    // Setup message form
                    setupMessageForm();
                })
                .catch(error => {
                    console.error('Error:', error);
                });
        }
        
        // Setup message form
        function setupMessageForm() {
            const messageForm = document.getElementById('message-form');
            
            if (messageForm) {
                messageForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    
                    const formData = new FormData(this);
                    
                    fetch(this.action, {
                        method: 'POST',
                        headers: {
                            'X-CSRFToken': getCookie('csrftoken')
                        },
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Add new message to the conversation
                            const messagesContainer = document.querySelector('.messages-container');
                            const messageElement = document.createElement('div');
                            messageElement.className = 'message outgoing';
                            messageElement.innerHTML = `
                                <div class="message-content">
                                    <div class="message-text">${data.message.content}</div>
                                    <div class="message-time">${data.message.time}</div>
                                </div>
                            `;
                            
                            messagesContainer.appendChild(messageElement);
                            messagesContainer.scrollTop = messagesContainer.scrollHeight;
                            
                            // Clear input
                            document.getElementById('message-input').value = '';
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                    });
                });
            }
        }
        
        // Helper function to get CSRF token
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
    });
</script>
{% endblock %}
