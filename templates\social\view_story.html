{% extends 'base.html' %}

{% block title %}{{ story.user.username }}'s Story | PetPaw{% endblock %}

{% block extra_css %}
<style>
    body {
        background-color: var(--gray-900);
    }
    
    .story-container {
        position: relative;
        max-width: 500px;
        height: 100vh;
        margin: 0 auto;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }
    
    .story-card {
        background-color: var(--black);
        border-radius: var(--radius-lg);
        overflow: hidden;
        position: relative;
    }
    
    .story-header {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: var(--spacing-base) var(--spacing-lg);
        background: linear-gradient(to bottom, rgba(0, 0, 0, 0.7), transparent);
        z-index: 10;
    }
    
    .story-user {
        display: flex;
        align-items: center;
        gap: var(--gap-base);
        color: var(--white);
    }
    
    .story-user-avatar {
        width: 40px;
        height: 40px;
        border-radius: var(--radius-full);
        object-fit: cover;
        border: 2px solid var(--white);
    }
    
    .story-user-info {
        display: flex;
        flex-direction: column;
    }
    
    .story-user-name {
        font-weight: var(--fw-medium);
    }
    
    .story-time {
        font-size: var(--font-xs);
        opacity: 0.8;
    }
    
    .story-close {
        background: none;
        border: none;
        color: var(--white);
        font-size: var(--font-xl);
        cursor: pointer;
    }
    
    .story-content {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .story-image {
        width: 100%;
        max-height: 90vh;
        object-fit: contain;
    }
    
    .story-video {
        width: 100%;
        max-height: 90vh;
        object-fit: contain;
    }
    
    .story-caption {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: var(--spacing-lg);
        background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
        color: var(--white);
        font-size: var(--font-lg);
        text-align: center;
    }
    
    .story-navigation {
        position: absolute;
        top: 0;
        bottom: 0;
        width: 100%;
        display: flex;
    }
    
    .story-nav-prev, .story-nav-next {
        flex: 1;
        background: none;
        border: none;
        cursor: pointer;
    }
    
    .story-progress {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        display: flex;
        gap: var(--gap-xs);
        padding: var(--spacing-xs) var(--spacing-lg);
        z-index: 20;
    }
    
    .story-progress-bar {
        flex: 1;
        height: 3px;
        background-color: rgba(255, 255, 255, 0.3);
        border-radius: var(--radius-full);
        overflow: hidden;
    }
    
    .story-progress-fill {
        height: 100%;
        background-color: var(--white);
        width: 0;
        transition: width 5s linear;
    }
    
    .story-progress-fill.active {
        width: 100%;
    }
</style>
{% endblock %}

{% block content %}
<div class="story-container">
    <div class="story-card">
        <div class="story-progress">
            <div class="story-progress-bar">
                <div class="story-progress-fill active"></div>
            </div>
        </div>
        
        <div class="story-header">
            <div class="story-user">
                <img src="{{ story.user.profile_picture.url }}" alt="{{ story.user.username }}" class="story-user-avatar">
                <div class="story-user-info">
                    <div class="story-user-name">{{ story.user.username }}</div>
                    <div class="story-time">{{ story.created_at|timesince }} ago</div>
                </div>
            </div>
            
            <a href="{% url 'feed' %}" class="story-close">
                <i class="fas fa-times"></i>
            </a>
        </div>
        
        <div class="story-content">
            {% if story.image %}
                <img src="{{ story.image.url }}" alt="{{ story.caption }}" class="story-image">
            {% elif story.video %}
                <video src="{{ story.video.url }}" autoplay muted loop class="story-video"></video>
            {% endif %}
        </div>
        
        {% if story.caption %}
            <div class="story-caption">
                {{ story.caption }}
            </div>
        {% endif %}
        
        <div class="story-navigation">
            {% if prev_story_id %}
                <a href="{% url 'view-story' pk=prev_story_id %}" class="story-nav-prev"></a>
            {% else %}
                <div class="story-nav-prev"></div>
            {% endif %}
            
            {% if next_story_id %}
                <a href="{% url 'view-story' pk=next_story_id %}" class="story-nav-next"></a>
            {% else %}
                <div class="story-nav-next"></div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-navigate to next story after 5 seconds
        const nextStoryId = '{{ next_story_id }}';
        
        if (nextStoryId) {
            setTimeout(function() {
                window.location.href = "{% url 'view-story' pk=next_story_id %}";
            }, 5000);
        } else {
            // If no next story, go back to feed after 5 seconds
            setTimeout(function() {
                window.location.href = "{% url 'feed' %}";
            }, 5000);
        }
        
        // Handle keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft' && '{{ prev_story_id }}') {
                window.location.href = "{% url 'view-story' pk=prev_story_id %}";
            } else if (e.key === 'ArrowRight' && '{{ next_story_id }}') {
                window.location.href = "{% url 'view-story' pk=next_story_id %}";
            } else if (e.key === 'Escape') {
                window.location.href = "{% url 'feed' %}";
            }
        });
    });
</script>
{% endblock %}
