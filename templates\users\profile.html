{% extends 'base.html' %}

{% block title %}{{ profile_user.username }}'s Profile | PetPaw{% endblock %}

{% block extra_css %}
<style>
    /* Modern Profile Header */
    .profile-header {
        background-color: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-base);
        padding: var(--spacing-xl);
        margin-bottom: var(--spacing-2xl);
        position: relative;
        overflow: hidden;
    }

    .profile-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 80px;
        background: linear-gradient(135deg, var(--primary), var(--primary-light));
        z-index: 0;
    }

    .profile-header-content {
        position: relative;
        z-index: 1;
        display: flex;
        align-items: center;
        gap: var(--spacing-xl);
    }

    .profile-picture-wrapper {
        position: relative;
    }

    .profile-picture {
        width: 150px;
        height: 150px;
        border-radius: var(--radius-full);
        object-fit: cover;
        border: 4px solid white;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }

    .profile-info {
        flex: 1;
    }

    .profile-username {
        font-size: var(--font-2xl);
        margin-bottom: var(--spacing-xs);
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
    }

    .verified-badge {
        color: var(--accent2);
        font-size: 1.2rem;
    }

    .profile-name {
        color: var(--text-light);
        margin-bottom: var(--spacing-base);
    }

    .profile-bio {
        margin-bottom: var(--spacing-lg);
        line-height: 1.6;
    }

    .profile-stats {
        display: flex;
        gap: var(--spacing-xl);
        margin-bottom: var(--spacing-lg);
    }

    .stat-item {
        text-align: center;
        cursor: pointer;
        transition: transform 0.2s;
    }

    .stat-item:hover {
        transform: translateY(-2px);
    }

    .stat-value {
        font-size: var(--font-xl);
        font-weight: var(--fw-bold);
    }

    .stat-label {
        color: var(--text-light);
        font-size: var(--font-sm);
    }

    .profile-actions {
        display: flex;
        gap: var(--spacing-base);
    }

    .btn-follow {
        background-color: var(--primary);
        color: var(--white);
        border: none;
        padding: var(--spacing-sm) var(--spacing-xl);
        border-radius: var(--radius-full);
        font-weight: var(--fw-semibold);
        cursor: pointer;
        transition: var(--transition-slow);
    }

    .btn-follow:hover {
        background-color: var(--primary-dark);
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }

    .btn-unfollow {
        background-color: transparent;
        color: var(--text);
        border: 1px solid var(--gray-300);
        padding: var(--spacing-sm) var(--spacing-xl);
        border-radius: var(--radius-full);
        font-weight: var(--fw-semibold);
        cursor: pointer;
        transition: var(--transition-slow);
    }

    .btn-unfollow:hover {
        background-color: var(--gray-100);
        box-shadow: var(--shadow-sm);
    }

    .btn-message {
        background-color: transparent;
        color: var(--text);
        border: 1px solid var(--gray-300);
        padding: var(--spacing-sm) var(--spacing-xl);
        border-radius: var(--radius-full);
        font-weight: var(--fw-semibold);
        cursor: pointer;
        transition: var(--transition-slow);
    }

    .btn-message:hover {
        background-color: var(--gray-100);
        box-shadow: var(--shadow-sm);
    }

    /* Tabs Styling */
    .profile-tabs {
        margin-bottom: var(--spacing-xl);
    }

    .tab-list {
        display: flex;
        list-style: none;
        padding: 0;
        margin: 0;
        border-bottom: 1px solid var(--gray-200);
    }

    .tab-item {
        margin-right: var(--spacing-xl);
    }

    .tab-link {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: var(--spacing-base) 0;
        color: var(--text-light);
        font-weight: var(--fw-medium);
        position: relative;
        transition: all 0.3s ease;
    }

    .tab-link i {
        font-size: 1.2rem;
    }

    .tab-link.active {
        color: var(--primary);
    }

    .tab-link.active::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        width: 100%;
        height: 2px;
        background-color: var(--primary);
    }

    /* Tab Content */
    .tab-content {
        position: relative;
        min-height: 200px;
    }

    .tab-pane {
        display: none;
    }

    .tab-pane.active {
        display: block;
        animation: fadeIn 0.5s ease;
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    /* Grid Layouts */
    .pet-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: var(--gap-xl);
    }

    .post-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: var(--gap-lg);
    }

    /* Card Styling */
    .card {
        background-color: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(0,0,0,0.05);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 16px rgba(0,0,0,0.1);
    }

    .card-img-top {
        width: 100%;
        height: 200px;
        object-fit: cover;
    }

    .card-body {
        padding: 1.25rem;
    }

    .card-title {
        font-size: 1.25rem;
        margin-bottom: 0.5rem;
    }

    .card-text {
        color: var(--text-light);
        margin-bottom: 1rem;
    }

    /* Post Card Styling */
    .post-card {
        position: relative;
        border-radius: 12px;
        overflow: hidden;
        aspect-ratio: 1/1;
    }

    .post-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .post-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.3);
        opacity: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        transition: var(--transition-slow);
    }

    .post-card:hover .post-overlay {
        opacity: 1;
    }

    .post-stats {
        display: flex;
        gap: var(--gap-xl);
        color: var(--white);
    }

    .post-stat {
        display: flex;
        align-items: center;
        gap: 5px;
    }

    .post-stat i {
        font-size: 1.2rem;
    }

    /* Empty State */
    .empty-state {
        text-align: center;
        padding: var(--spacing-4xl) var(--spacing-base);
        background-color: var(--gray-50);
        border-radius: var(--radius-xl);
        border: 1px dashed var(--gray-300);
    }

    .empty-state p {
        margin-bottom: var(--spacing-base);
        color: var(--text-light);
        font-size: var(--font-md);
    }

    /* Responsive Adjustments */
    @media (max-width: 768px) {
        .profile-header-content {
            flex-direction: column;
            text-align: center;
        }

        .profile-stats {
            justify-content: center;
        }

        .profile-actions {
            justify-content: center;
        }

        .post-grid {
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="profile-header">
        <div class="profile-header-content">
            <div class="profile-picture-wrapper">
                <img src="{{ profile_user.profile_picture.url }}" alt="{{ profile_user.username }}" class="profile-picture">
            </div>

            <div class="profile-info">
                <h1 class="profile-username">
                    {{ profile_user.username }}
                    {% if profile_user.is_service_provider %}
                        <span class="verified-badge" title="Verified Service Provider"><i class="fas fa-check-circle"></i></span>
                    {% endif %}
                </h1>
                <p class="profile-name">{{ profile_user.first_name }} {{ profile_user.last_name }}</p>

                <p class="profile-bio">{{ profile_user.bio|default:"No bio yet." }}</p>

                <div class="profile-stats">
                    <div class="stat-item">
                        <div class="stat-value">{{ profile_user.profile.get_pets_count }}</div>
                        <div class="stat-label">Pets</div>
                    </div>

                    <div class="stat-item">
                        <div class="stat-value">{{ profile_user.profile.get_followers_count }}</div>
                        <div class="stat-label">Followers</div>
                    </div>

                    <div class="stat-item">
                        <div class="stat-value">{{ profile_user.profile.get_following_count }}</div>
                        <div class="stat-label">Following</div>
                    </div>
                </div>

                <div class="profile-actions">
                    {% if user.is_authenticated and user != profile_user %}
                        {% if is_following %}
                            <a href="{% url 'follow-user' username=profile_user.username %}" class="btn-unfollow">
                                <i class="fas fa-user-minus"></i> Unfollow
                            </a>
                        {% else %}
                            <a href="{% url 'follow-user' username=profile_user.username %}" class="btn-follow">
                                <i class="fas fa-user-plus"></i> Follow
                            </a>
                        {% endif %}

                        <a href="{% url 'start-conversation' username=profile_user.username %}" class="btn-message">
                            <i class="fas fa-envelope"></i> Message
                        </a>
                    {% elif user == profile_user %}
                        <a href="/users/profile/edit/" class="btn-message">
                            <i class="fas fa-edit"></i> Edit Profile
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="profile-tabs">
        <ul class="tab-list">
            <li class="tab-item">
                <a href="#pets" class="tab-link active">
                    <i class="fas fa-paw"></i> Pets
                </a>
            </li>
            <li class="tab-item">
                <a href="#posts" class="tab-link">
                    <i class="fas fa-images"></i> Posts
                </a>
            </li>
            {% if user == profile_user %}
                <li class="tab-item">
                    <a href="#saved" class="tab-link">
                        <i class="fas fa-bookmark"></i> Saved
                    </a>
                </li>
            {% endif %}
        </ul>
    </div>

    <div class="tab-content">
        <!-- Pets Tab -->
        <div id="pets" class="tab-pane active">
            {% if pets %}
                <div class="pet-grid">
                    {% for pet in pets %}
                        <div class="card">
                            <img src="{{ pet.profile_picture.url }}" alt="{{ pet.name }}" class="card-img-top">
                            <div class="card-body">
                                <h3 class="card-title">{{ pet.name }}</h3>
                                <p class="card-text">{{ pet.breed.name }}</p>
                                <a href="{% url 'pet-detail' pk=pet.pk %}" class="btn btn-primary">View Profile</a>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="empty-state">
                    <p>No pets added yet.</p>
                    {% if user == profile_user %}
                        <a href="{% url 'pet-create' %}" class="btn btn-primary">Add a Pet</a>
                    {% endif %}
                </div>
            {% endif %}
        </div>

        <!-- Posts Tab -->
        <div id="posts" class="tab-pane">
            {% if profile_user.posts.all %}
                <div class="post-grid">
                    {% for post in profile_user.posts.all %}
                        <a href="{% url 'post-detail' pk=post.pk %}" class="post-card">
                            {% if post.image %}
                                <img src="{{ post.image.url }}" alt="{{ post.content|truncatechars:50 }}" class="post-img">
                            {% elif post.video %}
                                <div class="post-video-thumbnail">
                                    <i class="fas fa-play-circle"></i>
                                </div>
                            {% endif %}
                            <div class="post-overlay">
                                <div class="post-stats">
                                    <div class="post-stat">
                                        <i class="fas fa-heart"></i>
                                        <span>{{ post.get_like_count }}</span>
                                    </div>
                                    <div class="post-stat">
                                        <i class="fas fa-comment"></i>
                                        <span>{{ post.get_comment_count }}</span>
                                    </div>
                                </div>
                            </div>
                        </a>
                    {% endfor %}
                </div>
            {% else %}
                <div class="empty-state">
                    <p>No posts yet.</p>
                    {% if user == profile_user %}
                        <a href="{% url 'post-create' %}" class="btn btn-primary">Create a Post</a>
                    {% endif %}
                </div>
            {% endif %}
        </div>

        <!-- Saved Tab (only for profile owner) -->
        {% if user == profile_user %}
            <div id="saved" class="tab-pane">
                {% if user.liked_posts.all %}
                    <div class="post-grid">
                        {% for post in user.liked_posts.all %}
                            <a href="{% url 'post-detail' pk=post.pk %}" class="post-card">
                                {% if post.image %}
                                    <img src="{{ post.image.url }}" alt="{{ post.content|truncatechars:50 }}" class="post-img">
                                {% elif post.video %}
                                    <div class="post-video-thumbnail">
                                        <i class="fas fa-play-circle"></i>
                                    </div>
                                {% endif %}
                                <div class="post-overlay">
                                    <div class="post-stats">
                                        <div class="post-stat">
                                            <i class="fas fa-heart"></i>
                                            <span>{{ post.get_like_count }}</span>
                                        </div>
                                        <div class="post-stat">
                                            <i class="fas fa-comment"></i>
                                            <span>{{ post.get_comment_count }}</span>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="empty-state">
                        <p>No saved posts yet.</p>
                        <a href="{% url 'explore' %}" class="btn btn-primary">Explore Posts</a>
                    </div>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const tabLinks = document.querySelectorAll('.tab-link');
        const tabPanes = document.querySelectorAll('.tab-pane');

        // Function to activate a tab
        function activateTab(tabId) {
            // Remove active class from all tabs
            tabLinks.forEach(tab => tab.classList.remove('active'));
            tabPanes.forEach(pane => pane.classList.remove('active'));

            // Add active class to selected tab
            const selectedTab = document.querySelector(`.tab-link[href="#${tabId}"]`);
            if (selectedTab) {
                selectedTab.classList.add('active');
            }

            // Show corresponding tab content
            const selectedPane = document.getElementById(tabId);
            if (selectedPane) {
                selectedPane.classList.add('active');
            }

            // Update URL hash
            window.location.hash = tabId;
        }

        // Handle tab clicks
        tabLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const target = this.getAttribute('href').substring(1);
                activateTab(target);
            });
        });

        // Check for hash in URL on page load
        if (window.location.hash) {
            const tabId = window.location.hash.substring(1);
            const validTabs = Array.from(tabPanes).map(pane => pane.id);

            if (validTabs.includes(tabId)) {
                activateTab(tabId);
            }
        }

        // Add hover effects to post cards
        const postCards = document.querySelectorAll('.post-card');
        postCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.querySelector('.post-overlay').style.opacity = '1';
            });

            card.addEventListener('mouseleave', function() {
                this.querySelector('.post-overlay').style.opacity = '0';
            });
        });

        // Add animation to stats on hover
        const statItems = document.querySelectorAll('.stat-item');
        statItems.forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px)';
            });

            item.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    });
</script>
{% endblock %}
