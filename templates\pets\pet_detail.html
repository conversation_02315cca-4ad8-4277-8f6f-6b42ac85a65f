{% extends 'base.html' %}

{% block title %}{{ pet.name }} | PetPaw{% endblock %}

{% block extra_css %}
<style>
    .pet-detail-container {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--gap-2xl);
    }
    
    @media (max-width: 992px) {
        .pet-detail-container {
            grid-template-columns: 1fr;
        }
    }
    
    .pet-gallery {
        position: relative;
    }
    
    .pet-main-image {
        width: 100%;
        height: 400px;
        object-fit: cover;
        border-radius: var(--radius-lg);
        margin-bottom: var(--spacing-base);
    }
    
    .pet-thumbnails {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: var(--gap-sm);
    }
    
    .pet-thumbnail {
        width: 100%;
        height: 80px;
        object-fit: cover;
        border-radius: var(--radius-md);
        cursor: pointer;
        transition: var(--transition-base);
    }
    
    .pet-thumbnail:hover {
        opacity: 0.8;
    }
    
    .pet-thumbnail.active {
        border: 2px solid var(--primary);
    }
    
    .pet-info {
        background-color: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-base);
        padding: var(--spacing-2xl);
    }
    
    .pet-name {
        font-size: var(--font-3xl);
        margin-bottom: var(--spacing-xs);
    }
    
    .pet-breed {
        color: var(--text-light);
        margin-bottom: var(--spacing-lg);
        font-size: var(--font-lg);
    }
    
    .pet-meta {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: var(--gap-base);
        margin-bottom: var(--spacing-xl);
        padding-bottom: var(--spacing-xl);
        border-bottom: 1px solid var(--gray-200);
    }
    
    .pet-meta-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
    }
    
    .pet-meta-icon {
        font-size: var(--font-xl);
        color: var(--primary);
        margin-bottom: var(--spacing-xs);
    }
    
    .pet-meta-label {
        font-size: var(--font-sm);
        color: var(--text-light);
    }
    
    .pet-meta-value {
        font-weight: var(--fw-medium);
    }
    
    .pet-bio {
        margin-bottom: var(--spacing-xl);
    }
    
    .pet-bio-title {
        font-size: var(--font-lg);
        margin-bottom: var(--spacing-base);
    }
    
    .pet-adoption {
        background-color: var(--primary-light);
        border-radius: var(--radius-lg);
        padding: var(--spacing-xl);
        margin-bottom: var(--spacing-xl);
    }
    
    .pet-adoption-title {
        font-size: var(--font-lg);
        margin-bottom: var(--spacing-base);
        color: var(--primary-dark);
    }
    
    .pet-adoption-price {
        font-size: var(--font-2xl);
        font-weight: var(--fw-bold);
        color: var(--primary);
        margin-bottom: var(--spacing-base);
    }
    
    .pet-actions {
        display: flex;
        gap: var(--gap-base);
    }
    
    .pet-owner {
        display: flex;
        align-items: center;
        gap: var(--gap-base);
        margin-top: var(--spacing-xl);
        padding-top: var(--spacing-xl);
        border-top: 1px solid var(--gray-200);
    }
    
    .pet-owner-avatar {
        width: 50px;
        height: 50px;
        border-radius: var(--radius-full);
        object-fit: cover;
    }
    
    .pet-owner-info {
        flex: 1;
    }
    
    .pet-owner-name {
        font-weight: var(--fw-medium);
    }
    
    .pet-owner-joined {
        font-size: var(--font-sm);
        color: var(--text-light);
    }
    
    .pet-tabs {
        margin-top: var(--spacing-3xl);
    }
    
    .tab-list {
        display: flex;
        list-style: none;
        padding: 0;
        margin: 0;
        border-bottom: 1px solid var(--gray-200);
        margin-bottom: var(--spacing-xl);
    }
    
    .tab-item {
        margin-right: var(--spacing-xl);
    }
    
    .tab-link {
        display: block;
        padding: var(--spacing-base) 0;
        color: var(--text-light);
        font-weight: var(--fw-medium);
        position: relative;
    }
    
    .tab-link.active {
        color: var(--primary);
    }
    
    .tab-link.active::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        width: 100%;
        height: 2px;
        background-color: var(--primary);
    }
    
    .tab-pane {
        display: none;
    }
    
    .tab-pane.active {
        display: block;
    }
    
    .medical-records {
        background-color: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-base);
        overflow: hidden;
    }
    
    .medical-record {
        padding: var(--spacing-xl);
        border-bottom: 1px solid var(--gray-200);
    }
    
    .medical-record:last-child {
        border-bottom: none;
    }
    
    .medical-record-date {
        font-size: var(--font-sm);
        color: var(--text-light);
        margin-bottom: var(--spacing-xs);
    }
    
    .medical-record-title {
        font-size: var(--font-lg);
        margin-bottom: var(--spacing-sm);
    }
    
    .medical-record-vet {
        font-size: var(--font-sm);
        color: var(--text-light);
        margin-bottom: var(--spacing-base);
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="pet-detail-container">
        <div class="pet-gallery">
            <img src="{{ pet.profile_picture.url }}" alt="{{ pet.name }}" class="pet-main-image" id="main-image">
            
            <div class="pet-thumbnails">
                <img src="{{ pet.profile_picture.url }}" alt="{{ pet.name }}" class="pet-thumbnail active" data-src="{{ pet.profile_picture.url }}">
                
                {% for photo in pet.gallery.all %}
                    <img src="{{ photo.image.url }}" alt="{{ pet.name }}" class="pet-thumbnail" data-src="{{ photo.image.url }}">
                {% endfor %}
            </div>
        </div>
        
        <div class="pet-info">
            <h1 class="pet-name">{{ pet.name }}</h1>
            <p class="pet-breed">{{ pet.breed.name }}</p>
            
            <div class="pet-meta">
                <div class="pet-meta-item">
                    <div class="pet-meta-icon">
                        <i class="fas fa-venus-mars"></i>
                    </div>
                    <div class="pet-meta-label">Gender</div>
                    <div class="pet-meta-value">{{ pet.get_gender_display }}</div>
                </div>
                
                <div class="pet-meta-item">
                    <div class="pet-meta-icon">
                        <i class="fas fa-birthday-cake"></i>
                    </div>
                    <div class="pet-meta-label">Age</div>
                    <div class="pet-meta-value">{{ pet.birth_date|timesince }}</div>
                </div>
                
                <div class="pet-meta-item">
                    <div class="pet-meta-icon">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <div class="pet-meta-label">Location</div>
                    <div class="pet-meta-value">{{ pet.owner.location|default:"Not specified" }}</div>
                </div>
            </div>
            
            <div class="pet-bio">
                <h2 class="pet-bio-title">About {{ pet.name }}</h2>
                <p>{{ pet.bio }}</p>
            </div>
            
            {% if pet.is_for_adoption %}
                <div class="pet-adoption">
                    <h3 class="pet-adoption-title">Available for Adoption</h3>
                    {% if pet.adoption_price %}
                        <div class="pet-adoption-price">${{ pet.adoption_price }}</div>
                    {% endif %}
                    <p>This pet is looking for a loving home. Contact the owner for more information.</p>
                </div>
            {% endif %}
            
            <div class="pet-actions">
                {% if user.is_authenticated and user != pet.owner %}
                    {% if is_following %}
                        <a href="{% url 'follow-pet' pk=pet.pk %}" class="btn btn-outline">Unfollow</a>
                    {% else %}
                        <a href="{% url 'follow-pet' pk=pet.pk %}" class="btn btn-primary">Follow</a>
                    {% endif %}
                    
                    <a href="{% url 'start-conversation' username=pet.owner.username %}" class="btn btn-secondary">Contact Owner</a>
                {% elif user == pet.owner %}
                    <a href="{% url 'pet-update' pk=pet.pk %}" class="btn btn-primary">Edit Pet</a>
                    <a href="{% url 'add-pet-photo' pk=pet.pk %}" class="btn btn-outline">Add Photo</a>
                    <a href="{% url 'add-medical-record' pk=pet.pk %}" class="btn btn-outline">Add Medical Record</a>
                {% endif %}
            </div>
            
            <div class="pet-owner">
                <img src="{{ pet.owner.profile_picture.url }}" alt="{{ pet.owner.username }}" class="pet-owner-avatar">
                
                <div class="pet-owner-info">
                    <div class="pet-owner-name">{{ pet.owner.username }}</div>
                    <div class="pet-owner-joined">Member since {{ pet.owner.date_joined|date:"F Y" }}</div>
                </div>
                
                <a href="{% url 'user-profile' username=pet.owner.username %}" class="btn btn-sm btn-outline">View Profile</a>
            </div>
        </div>
    </div>
    
    <div class="pet-tabs">
        <ul class="tab-list">
            <li class="tab-item">
                <a href="#photos" class="tab-link active">Photos</a>
            </li>
            <li class="tab-item">
                <a href="#medical" class="tab-link">Medical Records</a>
            </li>
        </ul>
        
        <div class="tab-content">
            <div id="photos" class="tab-pane active">
                <div class="pet-photos-grid">
                    <!-- Photos will be displayed here -->
                </div>
            </div>
            
            <div id="medical" class="tab-pane">
                {% if pet.medical_records.all %}
                    <div class="medical-records">
                        {% for record in pet.medical_records.all %}
                            <div class="medical-record">
                                <div class="medical-record-date">{{ record.date|date:"F j, Y" }}</div>
                                <h3 class="medical-record-title">{{ record.title }}</h3>
                                <div class="medical-record-vet">Veterinarian: {{ record.veterinarian }}</div>
                                <p>{{ record.description }}</p>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="empty-state">
                        <p>No medical records available.</p>
                        {% if user == pet.owner %}
                            <a href="{% url 'add-medical-record' pk=pet.pk %}" class="btn btn-primary">Add Medical Record</a>
                        {% endif %}
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Image gallery
        const mainImage = document.getElementById('main-image');
        const thumbnails = document.querySelectorAll('.pet-thumbnail');
        
        thumbnails.forEach(thumbnail => {
            thumbnail.addEventListener('click', function() {
                // Update main image
                mainImage.src = this.getAttribute('data-src');
                
                // Update active thumbnail
                thumbnails.forEach(thumb => thumb.classList.remove('active'));
                this.classList.add('active');
            });
        });
        
        // Tabs
        const tabLinks = document.querySelectorAll('.tab-link');
        const tabPanes = document.querySelectorAll('.tab-pane');
        
        tabLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Remove active class from all tabs
                tabLinks.forEach(tab => tab.classList.remove('active'));
                tabPanes.forEach(pane => pane.classList.remove('active'));
                
                // Add active class to clicked tab
                this.classList.add('active');
                
                // Show corresponding tab content
                const target = this.getAttribute('href').substring(1);
                document.getElementById(target).classList.add('active');
            });
        });
    });
</script>
{% endblock %}
