from django.db import models
from django.contrib.auth.models import AbstractUser
from django.urls import reverse


class User(AbstractUser):
    """Custom user model for PetPaw application"""
    bio = models.TextField(max_length=500, blank=True)
    location = models.CharField(max_length=100, blank=True)
    birth_date = models.DateField(null=True, blank=True)
    profile_picture = models.ImageField(upload_to='profile_pics', default='default_profile.jpg')
    is_pet_owner = models.BooleanField(default=False)
    is_service_provider = models.BooleanField(default=False)
    phone_number = models.CharField(max_length=15, blank=True)
    
    def __str__(self):
        return self.username
    
    def get_absolute_url(self):
        return reverse('user-profile', kwargs={'username': self.username})


class UserProfile(models.Model):
    """Extended profile information for users"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    followers = models.ManyToManyField(User, related_name='following', blank=True)
    website = models.URLField(max_length=200, blank=True)
    interests = models.CharField(max_length=255, blank=True)
    
    def __str__(self):
        return f"{self.user.username}'s profile"
    
    def get_followers_count(self):
        return self.followers.count()
    
    def get_following_count(self):
        return self.user.following.count()
    
    def get_pets_count(self):
        return self.user.pets.count()


class Address(models.Model):
    """User address model for shipping and billing"""
    ADDRESS_TYPES = (
        ('shipping', 'Shipping'),
        ('billing', 'Billing'),
    )
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='addresses')
    address_type = models.CharField(max_length=10, choices=ADDRESS_TYPES)
    street_address = models.CharField(max_length=255)
    apartment_address = models.CharField(max_length=100, blank=True)
    city = models.CharField(max_length=100)
    state = models.CharField(max_length=100)
    country = models.CharField(max_length=100)
    zip_code = models.CharField(max_length=20)
    default = models.BooleanField(default=False)
    
    class Meta:
        verbose_name_plural = 'Addresses'
    
    def __str__(self):
        return f"{self.user.username}'s {self.address_type} address"
