from .models import Notification
from messaging.models import Conversation


def notifications(request):
    """Context processor to make notification counts available in all templates"""
    if request.user.is_authenticated:
        # Count unread notifications
        unread_notifications_count = Notification.objects.filter(
            recipient=request.user,
            is_read=False
        ).count()
        
        # Count unread messages
        conversations = Conversation.objects.filter(participants=request.user)
        unread_messages_count = 0
        for conversation in conversations:
            unread_messages_count += conversation.messages.filter(
                sender__in=conversation.participants.exclude(id=request.user.id),
                is_read=False
            ).count()
        
        return {
            'unread_notifications_count': unread_notifications_count,
            'unread_messages_count': unread_messages_count
        }
    return {
        'unread_notifications_count': 0,
        'unread_messages_count': 0
    }
