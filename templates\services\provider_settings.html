{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}Provider Settings - PetPaw{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3">
            <div class="card shadow-sm mb-4">
                <div class="card-body text-center">
                    {% if user.service_provider.profile_picture %}
                        <img src="{{ user.service_provider.profile_picture.url }}" alt="{{ user.username }}" class="rounded-circle img-fluid mb-3" style="width: 120px; height: 120px; object-fit: cover;">
                    {% else %}
                        <img src="/static/img/default-profile.png" alt="{{ user.username }}" class="rounded-circle img-fluid mb-3" style="width: 120px; height: 120px; object-fit: cover;">
                    {% endif %}
                    <h5 class="mb-0">{{ user.get_full_name|default:user.username }}</h5>
                    <p class="text-muted">Service Provider</p>
                    <div class="d-grid gap-2">
                        <a href="{% url 'provider-detail' user.service_provider.id %}" class="btn btn-outline-primary btn-sm">View Public Profile</a>
                    </div>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{% url 'provider-dashboard' %}" class="list-group-item list-group-item-action {% if active_tab == 'dashboard' %}active{% endif %}">
                        <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                    </a>
                    <a href="{% url 'provider-services' %}" class="list-group-item list-group-item-action {% if active_tab == 'services' %}active{% endif %}">
                        <i class="fas fa-concierge-bell me-2"></i> My Services
                    </a>
                    <a href="{% url 'provider-bookings' %}" class="list-group-item list-group-item-action {% if active_tab == 'bookings' %}active{% endif %}">
                        <i class="fas fa-calendar-check me-2"></i> Bookings
                    </a>
                    <a href="{% url 'provider-availability' %}" class="list-group-item list-group-item-action {% if active_tab == 'availability' %}active{% endif %}">
                        <i class="fas fa-clock me-2"></i> Availability
                    </a>
                    <a href="{% url 'provider-settings' %}" class="list-group-item list-group-item-action {% if active_tab == 'settings' %}active{% endif %}">
                        <i class="fas fa-cog me-2"></i> Settings
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-md-9">
            <!-- Provider Settings -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Provider Profile Settings</h5>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        <div class="row mb-4">
                            <div class="col-md-3 text-center">
                                {% if user.service_provider.profile_picture %}
                                    <img src="{{ user.service_provider.profile_picture.url }}" alt="{{ user.username }}" class="rounded-circle img-fluid mb-3" style="width: 150px; height: 150px; object-fit: cover;">
                                {% else %}
                                    <img src="/static/img/default-profile.png" alt="{{ user.username }}" class="rounded-circle img-fluid mb-3" style="width: 150px; height: 150px; object-fit: cover;">
                                {% endif %}
                                <div class="mb-3">
                                    {{ form.profile_picture|as_crispy_field }}
                                </div>
                            </div>
                            <div class="col-md-9">
                                <div class="row">
                                    <div class="col-md-6">
                                        {{ form.hourly_rate|as_crispy_field }}
                                    </div>
                                    <div class="col-md-6">
                                        {{ form.experience_years|as_crispy_field }}
                                    </div>
                                </div>
                                {{ form.is_available|as_crispy_field }}
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            {{ form.bio|as_crispy_field }}
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                {{ form.categories|as_crispy_field }}
                            </div>
                            <div class="col-md-6">
                                {{ form.pet_categories|as_crispy_field }}
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            {{ form.qualifications|as_crispy_field }}
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i> Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Gallery Management -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Gallery Management</h5>
                    <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addGalleryModal">
                        <i class="fas fa-plus-circle me-2"></i> Add Photos
                    </button>
                </div>
                <div class="card-body">
                    {% if gallery_images %}
                        <div class="row">
                            {% for image in gallery_images %}
                                <div class="col-md-4 col-sm-6 mb-4">
                                    <div class="card h-100">
                                        <img src="{{ image.image.url }}" class="card-img-top" alt="Gallery Image" style="height: 200px; object-fit: cover;">
                                        <div class="card-body">
                                            <p class="card-text small">{{ image.caption|default:"No caption" }}</p>
                                            <form action="{% url 'delete-gallery-image' image.id %}" method="post">
                                                {% csrf_token %}
                                                <button type="submit" class="btn btn-sm btn-outline-danger w-100" onclick="return confirm('Are you sure you want to delete this image?')">
                                                    <i class="fas fa-trash-alt me-2"></i> Delete
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-images fa-3x text-muted mb-3"></i>
                            <h5>No Gallery Images</h5>
                            <p class="text-muted">Add photos to showcase your services and facilities.</p>
                            <button type="button" class="btn btn-primary mt-2" data-bs-toggle="modal" data-bs-target="#addGalleryModal">
                                <i class="fas fa-plus-circle me-2"></i> Add Photos
                            </button>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Gallery Image Modal -->
<div class="modal fade" id="addGalleryModal" tabindex="-1" aria-labelledby="addGalleryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addGalleryModalLabel">Add Gallery Image</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'add-gallery-image' %}" enctype="multipart/form-data">
                <div class="modal-body">
                    {% csrf_token %}
                    {{ gallery_form|crispy }}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Upload Image</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
