{% extends "base.html" %}

{% load i18n %}
{% load account %}

{% block title %}{% trans "Password Reset" %} | PetPaw{% endblock %}

{% block extra_css %}
<style>
    .auth-container {
        max-width: 500px;
        margin: 0 auto;
        padding: var(--spacing-2xl) 0;
    }
    
    .auth-card {
        background-color: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-base);
        padding: var(--spacing-2xl);
    }
    
    .auth-header {
        text-align: center;
        margin-bottom: var(--spacing-xl);
    }
    
    .auth-title {
        font-size: var(--font-2xl);
        margin-bottom: var(--spacing-sm);
    }
    
    .auth-subtitle {
        color: var(--text-light);
    }
    
    .auth-form {
        margin-top: var(--spacing-xl);
    }
    
    .form-group {
        margin-bottom: var(--spacing-lg);
    }
    
    .form-label {
        display: block;
        margin-bottom: var(--spacing-xs);
        font-weight: var(--fw-medium);
    }
    
    .form-control {
        width: 100%;
        padding: var(--spacing-sm) var(--spacing-base);
        border: 1px solid var(--gray-300);
        border-radius: var(--radius-md);
        font-family: var(--font-family-sans);
        font-size: var(--font-base);
        transition: var(--transition-base);
    }
    
    .form-control:focus {
        outline: none;
        border-color: var(--primary);
        box-shadow: 0 0 0 3px var(--primary-light);
    }
    
    .form-text {
        display: block;
        margin-top: var(--spacing-xs);
        font-size: var(--font-sm);
        color: var(--text-light);
    }
    
    .auth-submit {
        width: 100%;
        margin-top: var(--spacing-base);
    }
    
    .auth-footer {
        text-align: center;
        margin-top: var(--spacing-xl);
    }
    
    .auth-footer a {
        color: var(--primary);
        font-weight: var(--fw-medium);
    }
    
    .errorlist {
        color: var(--danger);
        list-style: none;
        padding: 0;
        margin: var(--spacing-xs) 0 0;
        font-size: var(--font-sm);
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <h1 class="auth-title">{% trans "Password Reset" %}</h1>
                <p class="auth-subtitle">{% trans "Forgot your password? Enter your email below to receive a password reset link." %}</p>
            </div>
            
            {% if user.is_authenticated %}
                <div class="alert alert-info">
                    {% include "account/snippets/already_logged_in.html" %}
                </div>
            {% endif %}
            
            <form class="auth-form" method="post" action="{% url 'account_reset_password' %}">
                {% csrf_token %}
                
                {% if form.non_field_errors %}
                    <div class="alert alert-danger">
                        {% for error in form.non_field_errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}
                
                <div class="form-group">
                    <label for="{{ form.email.id_for_label }}" class="form-label">{% trans "Email" %}</label>
                    {{ form.email }}
                    {% if form.email.errors %}
                        <ul class="errorlist">
                            {% for error in form.email.errors %}
                                <li>{{ error }}</li>
                            {% endfor %}
                        </ul>
                    {% endif %}
                </div>
                
                <button type="submit" class="btn btn-primary auth-submit">{% trans "Reset My Password" %}</button>
            </form>
            
            <div class="auth-footer">
                <p>{% trans "Remember your password?" %} <a href="{% url 'account_login' %}">{% trans "Sign In" %}</a></p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
