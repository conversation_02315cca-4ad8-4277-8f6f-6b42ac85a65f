from django.urls import path
from . import views

urlpatterns = [
    path('', views.PetListView.as_view(), name='pet-list'),
    path('<int:pk>/', views.PetDetailView.as_view(), name='pet-detail'),
    path('new/', views.PetCreateView.as_view(), name='pet-create'),
    path('<int:pk>/update/', views.PetUpdateView.as_view(), name='pet-update'),
    path('<int:pk>/delete/', views.PetDeleteView.as_view(), name='pet-delete'),
    path('<int:pk>/follow/', views.follow_pet, name='follow-pet'),
    path('<int:pk>/add-photo/', views.add_pet_photo, name='add-pet-photo'),
    path('<int:pk>/add-medical-record/', views.add_medical_record, name='add-medical-record'),
    path('ajax/load-breeds/', views.load_breeds, name='ajax-load-breeds'),
]
