{% extends 'base.html' %}
{% load static %}

{% block title %}Your Orders | PetPaw{% endblock %}

{% block extra_css %}
<style>
    .orders-container {
        max-width: 1000px;
        margin: 0 auto;
    }
    
    .orders-header {
        margin-bottom: var(--spacing-2xl);
    }
    
    .orders-title {
        margin-bottom: var(--spacing-xs);
    }
    
    .orders-list {
        background-color: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-base);
        overflow: hidden;
    }
    
    .order-item {
        padding: var(--spacing-xl);
        border-bottom: 1px solid var(--gray-200);
        display: grid;
        grid-template-columns: 1fr 1fr 1fr auto;
        gap: var(--gap-xl);
        align-items: center;
    }
    
    .order-item:last-child {
        border-bottom: none;
    }
    
    .order-info {
        display: flex;
        flex-direction: column;
        gap: var(--gap-xs);
    }
    
    .order-number {
        font-weight: var(--fw-medium);
        font-size: var(--font-lg);
    }
    
    .order-date {
        color: var(--text-light);
        font-size: var(--font-sm);
    }
    
    .order-status {
        display: inline-block;
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--radius-md);
        font-size: var(--font-sm);
        font-weight: var(--fw-medium);
        text-transform: capitalize;
    }
    
    .status-pending {
        background-color: var(--warning-light);
        color: var(--warning);
    }
    
    .status-processing {
        background-color: var(--info-light);
        color: var(--info);
    }
    
    .status-shipped {
        background-color: var(--primary-light);
        color: var(--primary);
    }
    
    .status-delivered {
        background-color: var(--success-light);
        color: var(--success);
    }
    
    .status-cancelled {
        background-color: var(--danger-light);
        color: var(--danger);
    }
    
    .order-total {
        font-weight: var(--fw-bold);
        font-size: var(--font-lg);
        color: var(--primary);
    }
    
    .order-actions {
        display: flex;
        justify-content: flex-end;
    }
    
    .view-order-btn {
        display: inline-flex;
        align-items: center;
        gap: var(--gap-xs);
        padding: var(--spacing-sm) var(--spacing-base);
        background-color: var(--primary);
        color: var(--white);
        border-radius: var(--radius-md);
        font-weight: var(--fw-medium);
        transition: var(--transition-base);
        text-decoration: none;
    }
    
    .view-order-btn:hover {
        background-color: var(--primary-dark);
    }
    
    .empty-orders {
        text-align: center;
        padding: var(--spacing-3xl) var(--spacing-xl);
        background-color: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-base);
    }
    
    .empty-orders-icon {
        font-size: 4rem;
        color: var(--gray-300);
        margin-bottom: var(--spacing-xl);
    }
    
    .empty-orders-message {
        font-size: var(--font-lg);
        margin-bottom: var(--spacing-xl);
        color: var(--text-light);
    }
    
    @media (max-width: 768px) {
        .order-item {
            grid-template-columns: 1fr;
            gap: var(--gap-base);
        }
        
        .order-actions {
            justify-content: flex-start;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="orders-container">
        <div class="orders-header">
            <h1 class="orders-title">Your Orders</h1>
            <p>View and track your order history</p>
        </div>
        
        {% if orders %}
            <div class="orders-list">
                {% for order in orders %}
                    <div class="order-item">
                        <div class="order-info">
                            <div class="order-number">Order #{{ order.id }}</div>
                            <div class="order-date">{{ order.created_at|date:"F j, Y" }}</div>
                        </div>
                        
                        <div class="order-status status-{{ order.status }}">
                            {{ order.status }}
                        </div>
                        
                        <div class="order-total">${{ order.total_price }}</div>
                        
                        <div class="order-actions">
                            <a href="{% url 'order-detail' pk=order.pk %}" class="view-order-btn">
                                <i class="fas fa-eye"></i> View Details
                            </a>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="empty-orders">
                <div class="empty-orders-icon">
                    <i class="fas fa-box-open"></i>
                </div>
                <h2 class="empty-orders-message">You haven't placed any orders yet</h2>
                <p>When you place orders, they will appear here for you to track.</p>
                <a href="{% url 'product-list' %}" class="btn btn-primary">Start Shopping</a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
