{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Products" %} | PetPaw{% endblock %}

{% block content %}
<div class="container py-5">
    <h1>{% trans "Our Products" %}</h1>

    {% if products %}
        <p>{% trans "Display product listing here..." %}</p>
        {# Loop through products and display them #}
    {% else %}
        <p>{% trans "No products found." %}{% if request.GET.search %} {% trans "for your search:" %} "{{ request.GET.search }}"{% endif %}</p>
    {% endif %}
    <!-- You'll want to add actual product display logic here -->
</div>
{% endblock %}