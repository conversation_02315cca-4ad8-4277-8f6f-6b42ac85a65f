{% extends 'base.html' %}

{% block title %}Your Cart | PetPaw{% endblock %}

{% block extra_css %}
<style>
    .cart-container {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: var(--gap-2xl);
    }
    
    @media (max-width: 992px) {
        .cart-container {
            grid-template-columns: 1fr;
        }
    }
    
    .cart-header {
        margin-bottom: var(--spacing-2xl);
    }
    
    .cart-title {
        margin-bottom: var(--spacing-xs);
    }
    
    .cart-items {
        background-color: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-base);
        overflow: hidden;
    }
    
    .cart-item {
        display: grid;
        grid-template-columns: auto 1fr auto;
        gap: var(--gap-xl);
        padding: var(--spacing-xl);
        border-bottom: 1px solid var(--gray-200);
    }
    
    .cart-item:last-child {
        border-bottom: none;
    }
    
    .cart-item-image {
        width: 100px;
        height: 100px;
        object-fit: cover;
        border-radius: var(--radius-md);
    }
    
    .cart-item-details {
        display: flex;
        flex-direction: column;
        justify-content: center;
    }
    
    .cart-item-name {
        font-size: var(--font-lg);
        margin-bottom: var(--spacing-xs);
    }
    
    .cart-item-category {
        color: var(--text-light);
        font-size: var(--font-sm);
        margin-bottom: var(--spacing-sm);
    }
    
    .cart-item-price {
        font-weight: var(--fw-medium);
        color: var(--primary);
    }
    
    .cart-item-actions {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-end;
        gap: var(--gap-base);
    }
    
    .cart-item-quantity-form {
        display: flex;
        align-items: center;
    }
    
    .quantity-input-group {
        display: flex;
        align-items: center;
        border: 1px solid var(--gray-300);
        border-radius: var(--radius-md);
        overflow: hidden;
    }
    
    .quantity-btn {
        background: none;
        border: none;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: var(--font-base);
        color: var(--text);
        transition: var(--transition-base);
    }
    
    .quantity-btn:hover {
        background-color: var(--gray-100);
    }
    
    .quantity-input {
        width: 40px;
        height: 30px;
        border: none;
        border-left: 1px solid var(--gray-300);
        border-right: 1px solid var(--gray-300);
        text-align: center;
        font-size: var(--font-sm);
    }
    
    .cart-item-total {
        font-weight: var(--fw-bold);
        font-size: var(--font-lg);
    }
    
    .cart-item-remove {
        color: var(--danger);
        background: none;
        border: none;
        cursor: pointer;
        font-size: var(--font-sm);
        display: flex;
        align-items: center;
        gap: var(--gap-xs);
        padding: 0;
        transition: var(--transition-base);
    }
    
    .cart-item-remove:hover {
        text-decoration: underline;
    }
    
    .cart-summary {
        background-color: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-base);
        padding: var(--spacing-xl);
        height: fit-content;
    }
    
    .summary-title {
        font-size: var(--font-xl);
        margin-bottom: var(--spacing-lg);
        padding-bottom: var(--spacing-sm);
        border-bottom: 1px solid var(--gray-200);
    }
    
    .summary-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: var(--spacing-sm);
    }
    
    .summary-label {
        color: var(--text-light);
    }
    
    .summary-value {
        font-weight: var(--fw-medium);
    }
    
    .summary-total {
        display: flex;
        justify-content: space-between;
        margin-top: var(--spacing-lg);
        padding-top: var(--spacing-lg);
        border-top: 1px solid var(--gray-200);
        font-size: var(--font-lg);
    }
    
    .summary-total-label {
        font-weight: var(--fw-medium);
    }
    
    .summary-total-value {
        font-weight: var(--fw-bold);
        color: var(--primary);
    }
    
    .checkout-button {
        width: 100%;
        margin-top: var(--spacing-xl);
    }
    
    .continue-shopping {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--gap-xs);
        margin-top: var(--spacing-base);
        color: var(--text);
        transition: var(--transition-base);
    }
    
    .continue-shopping:hover {
        color: var(--primary);
    }
    
    .empty-cart {
        text-align: center;
        padding: var(--spacing-3xl) var(--spacing-xl);
        background-color: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-base);
    }
    
    .empty-cart-icon {
        font-size: 4rem;
        color: var(--gray-300);
        margin-bottom: var(--spacing-xl);
    }
    
    .empty-cart-message {
        font-size: var(--font-lg);
        margin-bottom: var(--spacing-xl);
        color: var(--text-light);
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="cart-header">
        <h1 class="cart-title">Your Shopping Cart</h1>
        <p>Review your items and proceed to checkout</p>
    </div>
    
    {% if cart.items.all %}
        <div class="cart-container">
            <div class="cart-items">
                {% for item in cart.items.all %}
                    <div class="cart-item">
                        <img src="{{ item.product.image.url }}" alt="{{ item.product.name }}" class="cart-item-image">
                        
                        <div class="cart-item-details">
                            <h3 class="cart-item-name">{{ item.product.name }}</h3>
                            <div class="cart-item-category">{{ item.product.category.name }}</div>
                            <div class="cart-item-price">${{ item.product.price }} each</div>
                        </div>
                        
                        <div class="cart-item-actions">
                            <form method="post" action="{% url 'update-cart-item' pk=item.pk %}" class="cart-item-quantity-form">
                                {% csrf_token %}
                                <div class="quantity-input-group">
                                    <button type="button" class="quantity-btn decrement-btn">-</button>
                                    <input type="number" name="quantity" value="{{ item.quantity }}" min="1" max="{{ item.product.stock }}" class="quantity-input">
                                    <button type="button" class="quantity-btn increment-btn">+</button>
                                </div>
                            </form>
                            
                            <div class="cart-item-total">${{ item.get_total }}</div>
                            
                            <form method="post" action="{% url 'remove-from-cart' pk=item.pk %}">
                                {% csrf_token %}
                                <button type="submit" class="cart-item-remove">
                                    <i class="fas fa-trash-alt"></i> Remove
                                </button>
                            </form>
                        </div>
                    </div>
                {% endfor %}
            </div>
            
            <div class="cart-summary">
                <h2 class="summary-title">Order Summary</h2>
                
                <div class="summary-row">
                    <div class="summary-label">Subtotal ({{ cart.get_total_items }} items)</div>
                    <div class="summary-value">${{ cart.get_subtotal }}</div>
                </div>
                
                <div class="summary-row">
                    <div class="summary-label">Shipping</div>
                    <div class="summary-value">
                        {% if cart.get_subtotal >= 50 %}
                            Free
                        {% else %}
                            $5.99
                        {% endif %}
                    </div>
                </div>
                
                <div class="summary-row">
                    <div class="summary-label">Tax</div>
                    <div class="summary-value">${{ cart.get_tax }}</div>
                </div>
                
                <div class="summary-total">
                    <div class="summary-total-label">Total</div>
                    <div class="summary-total-value">${{ cart.get_total }}</div>
                </div>
                
                <a href="{% url 'checkout' %}" class="btn btn-primary checkout-button">Proceed to Checkout</a>
                <a href="{% url 'product-list' %}" class="continue-shopping">
                    <i class="fas fa-arrow-left"></i> Continue Shopping
                </a>
            </div>
        </div>
    {% else %}
        <div class="empty-cart">
            <div class="empty-cart-icon">
                <i class="fas fa-shopping-cart"></i>
            </div>
            <h2 class="empty-cart-message">Your cart is empty</h2>
            <p>Looks like you haven't added any products to your cart yet.</p>
            <a href="{% url 'product-list' %}" class="btn btn-primary">Start Shopping</a>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Quantity increment/decrement
        const quantityForms = document.querySelectorAll('.cart-item-quantity-form');
        
        quantityForms.forEach(form => {
            const decrementBtn = form.querySelector('.decrement-btn');
            const incrementBtn = form.querySelector('.increment-btn');
            const quantityInput = form.querySelector('.quantity-input');
            
            if (decrementBtn && incrementBtn && quantityInput) {
                decrementBtn.addEventListener('click', function() {
                    let value = parseInt(quantityInput.value);
                    if (value > 1) {
                        quantityInput.value = value - 1;
                        form.submit();
                    }
                });
                
                incrementBtn.addEventListener('click', function() {
                    let value = parseInt(quantityInput.value);
                    let max = parseInt(quantityInput.getAttribute('max'));
                    
                    if (value < max) {
                        quantityInput.value = value + 1;
                        form.submit();
                    }
                });
                
                quantityInput.addEventListener('change', function() {
                    form.submit();
                });
            }
        });
    });
</script>
{% endblock %}
