from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from messaging.models import Conversation, Message
from social.models import Notification

User = get_user_model()


class Command(BaseCommand):
    help = 'Send a test message between users'

    def add_arguments(self, parser):
        parser.add_argument('--from-user', type=str, help='Username of sender')
        parser.add_argument('--to-user', type=str, help='Username of recipient')
        parser.add_argument('--message', type=str, help='Message content')

    def handle(self, *args, **options):
        from_username = options.get('from_user', 'testuser1')
        to_username = options.get('to_user', 'testuser2')
        message_content = options.get('message', 'Hello! This is a test message.')

        try:
            from_user = User.objects.get(username=from_username)
            to_user = User.objects.get(username=to_username)
        except User.DoesNotExist as e:
            self.stdout.write(self.style.ERROR(f'User not found: {e}'))
            return

        # Get or create conversation
        conversations = Conversation.objects.filter(participants=from_user).filter(participants=to_user)
        if conversations.exists():
            conversation = conversations.first()
        else:
            conversation = Conversation.objects.create()
            conversation.participants.add(from_user, to_user)

        # Create message
        message = Message.objects.create(
            conversation=conversation,
            sender=from_user,
            content=message_content
        )

        # Create notification
        notification = Notification.objects.create(
            recipient=to_user,
            sender=from_user,
            notification_type='message',
            conversation=conversation,
            message=f"{from_user.username} sent you a message."
        )

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully sent message from {from_user.username} to {to_user.username}!\n'
                f'Message: "{message_content}"\n'
                f'Conversation ID: {conversation.id}\n'
                f'Notification ID: {notification.id}'
            )
        )
