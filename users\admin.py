from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from .models import User, UserProfile, Address


class UserProfileInline(admin.StackedInline):
    model = UserProfile
    can_delete = False
    verbose_name_plural = 'Profile'


class AddressInline(admin.TabularInline):
    model = Address
    extra = 0


class CustomUserAdmin(UserAdmin):
    inlines = (UserProfileInline, AddressInline)
    list_display = ('username', 'email', 'first_name', 'last_name', 'is_staff', 'is_pet_owner', 'is_service_provider')
    list_filter = ('is_staff', 'is_superuser', 'is_active', 'is_pet_owner', 'is_service_provider')
    fieldsets = UserAdmin.fieldsets + (
        ('Additional Info', {'fields': ('bio', 'location', 'birth_date', 'profile_picture', 
                                       'is_pet_owner', 'is_service_provider', 'phone_number')}),
    )


admin.site.register(User, CustomUserAdmin)
admin.site.register(Address)
