# Generated by Django 4.2.7 on 2025-05-24 08:56

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('social', '0003_notification_conversation_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='notification',
            name='story',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='social.story'),
        ),
        migrations.AddField(
            model_name='notification',
            name='story_comment_text',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='notification',
            name='notification_type',
            field=models.CharField(choices=[('like', 'Like'), ('comment', 'Comment'), ('follow', 'Follow'), ('mention', 'Mention'), ('message', 'Message'), ('story_like', 'Story Like'), ('story_comment', 'Story Comment')], max_length=15),
        ),
    ]
