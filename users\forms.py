from django import forms
from django.contrib.auth.forms import User<PERSON>reationForm, UserChangeForm
from .models import User, UserProfile, Address


class CustomUserCreationForm(UserCreationForm):
    """Form for user registration"""
    email = forms.EmailField(required=True)
    
    class Meta:
        model = User
        fields = ('username', 'email', 'password1', 'password2')
    
    def save(self, commit=True):
        user = super().save(commit=False)
        user.email = self.cleaned_data['email']
        if commit:
            user.save()
        return user


class UserProfileForm(forms.ModelForm):
    """Form for updating user profile"""
    class Meta:
        model = User
        fields = ('first_name', 'last_name', 'email', 'bio', 'location', 
                  'birth_date', 'profile_picture', 'phone_number')
        widgets = {
            'birth_date': forms.DateInput(attrs={'type': 'date'}),
            'bio': forms.Textarea(attrs={'rows': 4}),
        }


class ExtendedProfileForm(forms.ModelForm):
    """Form for updating extended profile information"""
    class Meta:
        model = UserProfile
        fields = ('website', 'interests')


class AddressForm(forms.ModelForm):
    """Form for adding/updating user addresses"""
    class Meta:
        model = Address
        fields = ('address_type', 'street_address', 'apartment_address', 
                  'city', 'state', 'country', 'zip_code', 'default')
        widgets = {
            'address_type': forms.Select(attrs={'class': 'form-select'}),
        }


class LoginForm(forms.Form):
    login = forms.CharField(
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'id': 'default-input',
            'placeholder': 'Enter your username or email'
        })
    )
    password = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter your password'
        })
    )
    remember = forms.BooleanField(required=False)
