from django import forms
from .models import Post, Comment, Story
from pets.models import Pet


class PostForm(forms.ModelForm):
    """Form for creating posts"""
    pet = forms.ModelChoiceField(queryset=Pet.objects.none(), required=False)
    
    class Meta:
        model = Post
        fields = ('content', 'post_type', 'image', 'video', 'pet')
        widgets = {
            'content': forms.Textarea(attrs={'rows': 4, 'placeholder': 'What\'s on your mind?'}),
        }
    
    def __init__(self, user, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if user:
            self.fields['pet'].queryset = Pet.objects.filter(owner=user)
        
        # Make fields required based on post_type
        self.fields['post_type'].widget = forms.RadioSelect(choices=Post.POST_TYPES)
        
        # Add custom validation for file types
        self.fields['image'].widget.attrs.update({'accept': 'image/*'})
        self.fields['video'].widget.attrs.update({'accept': 'video/*'})


class CommentForm(forms.ModelForm):
    """Form for adding comments"""
    class Meta:
        model = Comment
        fields = ('content',)
        widgets = {
            'content': forms.Textarea(attrs={'rows': 2, 'placeholder': 'Add a comment...'}),
        }
        labels = {
            'content': '',
        }


class StoryForm(forms.ModelForm):
    """Form for creating stories"""
    pet = forms.ModelChoiceField(queryset=Pet.objects.none(), required=False)
    
    class Meta:
        model = Story
        fields = ('image', 'video', 'caption', 'pet')
        widgets = {
            'caption': forms.TextInput(attrs={'placeholder': 'Add a caption...'}),
        }
    
    def __init__(self, user, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if user:
            self.fields['pet'].queryset = Pet.objects.filter(owner=user)
        
        # Add custom validation for file types
        self.fields['image'].widget.attrs.update({'accept': 'image/*'})
        self.fields['video'].widget.attrs.update({'accept': 'video/*'})
    
    def clean(self):
        cleaned_data = super().clean()
        image = cleaned_data.get('image')
        video = cleaned_data.get('video')
        
        if not image and not video:
            raise forms.ValidationError("You must upload either an image or a video.")
        
        if image and video:
            raise forms.ValidationError("You can only upload either an image or a video, not both.")
        
        return cleaned_data
