from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Q
from django.http import JsonResponse
from .models import Conversation, Message
from .forms import MessageForm
from users.models import User


@login_required
def inbox(request):
    """View for displaying user's conversations"""
    conversations = Conversation.objects.filter(participants=request.user)

    # Count unread messages for each conversation
    for conversation in conversations:
        conversation.unread_count = conversation.messages.filter(
            sender__in=conversation.participants.exclude(id=request.user.id),
            is_read=False
        ).count()

    return render(request, 'messaging/inbox.html', {'conversations': conversations})


@login_required
def conversation_detail(request, pk):
    """View for displaying a conversation and sending messages"""
    conversation = get_object_or_404(Conversation, pk=pk, participants=request.user)

    # Get all conversations for the sidebar
    conversations = Conversation.objects.filter(participants=request.user)

    # Count unread messages for each conversation
    for conv in conversations:
        conv.unread_count = conv.messages.filter(
            sender__in=conv.participants.exclude(id=request.user.id),
            is_read=False
        ).count()
        # Add other_user attribute for template compatibility
        conv.other_user = conv.get_other_participant(request.user)
        # Add last_message for template compatibility
        conv.last_message = conv.get_last_message()

    # Mark all messages as read
    unread_messages = conversation.messages.filter(
        sender__in=conversation.participants.exclude(id=request.user.id),
        is_read=False
    )
    for msg in unread_messages:
        msg.mark_as_read()

    if request.method == 'POST':
        form = MessageForm(request.POST, request.FILES)
        if form.is_valid():
            message = form.save(commit=False)
            message.conversation = conversation
            message.sender = request.user
            message.save()

            # Update conversation timestamp
            conversation.save()  # This will update the updated_at field

            # Handle AJAX requests
            if request.headers.get('x-requested-with') == 'XMLHttpRequest':
                return JsonResponse({
                    'status': 'success',
                    'message': {
                        'id': message.id,
                        'content': message.content,
                        'sender': message.sender.username,
                        'created_at': message.created_at.strftime('%b %d, %Y, %I:%M %p'),
                        'image_url': message.image.url if message.image else None
                    }
                })

            return redirect('conversation-detail', pk=pk)
    else:
        form = MessageForm()

    return render(request, 'messaging/conversation_detail.html', {
        'conversation': conversation,
        'conversations': conversations,
        'messages': conversation.messages.all(),
        'form': form,
        'other_user': conversation.get_other_participant(request.user)
    })


@login_required
def start_conversation(request, username):
    """View for starting a new conversation with a user"""
    other_user = get_object_or_404(User, username=username)

    # Check if conversation already exists
    conversations = Conversation.objects.filter(participants=request.user).filter(participants=other_user)

    if conversations.exists():
        # Conversation already exists, redirect to it
        return redirect('conversation-detail', pk=conversations.first().id)

    # Create new conversation
    conversation = Conversation.objects.create()
    conversation.participants.add(request.user, other_user)

    return redirect('conversation-detail', pk=conversation.id)


@login_required
def load_messages(request, pk):
    """AJAX view for loading messages in a conversation"""
    conversation = get_object_or_404(Conversation, pk=pk, participants=request.user)

    # Get messages after a certain ID if provided
    last_id = request.GET.get('last_id')
    if last_id:
        messages_list = conversation.messages.filter(id__gt=last_id)
    else:
        messages_list = conversation.messages.all()

    # Mark messages as read
    for msg in messages_list:
        if msg.sender != request.user and not msg.is_read:
            msg.mark_as_read()

    # Format messages for JSON response
    messages_data = []
    for msg in messages_list:
        messages_data.append({
            'id': msg.id,
            'content': msg.content,
            'sender': msg.sender.username,
            'is_self': msg.sender == request.user,
            'created_at': msg.created_at.strftime('%b %d, %Y, %I:%M %p'),
            'image_url': msg.image.url if msg.image else None
        })

    return JsonResponse({'messages': messages_data})
