from django.contrib import admin
from .models import Post, Comment, Story, Notification, Hashtag


class CommentInline(admin.TabularInline):
    model = Comment
    extra = 0


@admin.register(Post)
class PostAdmin(admin.ModelAdmin):
    list_display = ('user', 'pet', 'post_type', 'created_at', 'get_like_count', 'get_comment_count')
    list_filter = ('post_type', 'created_at')
    search_fields = ('user__username', 'content', 'pet__name')
    inlines = [CommentInline]


@admin.register(Comment)
class CommentAdmin(admin.ModelAdmin):
    list_display = ('user', 'post', 'content', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('user__username', 'content', 'post__content')


@admin.register(Story)
class StoryAdmin(admin.ModelAdmin):
    list_display = ('user', 'pet', 'caption', 'created_at', 'expires_at', 'is_expired')
    list_filter = ('created_at',)
    search_fields = ('user__username', 'caption', 'pet__name')


@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    list_display = ('recipient', 'sender', 'notification_type', 'is_read', 'created_at')
    list_filter = ('notification_type', 'is_read', 'created_at')
    search_fields = ('recipient__username', 'sender__username', 'message')


@admin.register(Hashtag)
class HashtagAdmin(admin.ModelAdmin):
    list_display = ('name', 'get_post_count')
    search_fields = ('name',)
