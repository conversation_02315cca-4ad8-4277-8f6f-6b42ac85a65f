from django import forms
from .models import Review


class ReviewForm(forms.ModelForm):
    """Form for product reviews"""
    class Meta:
        model = Review
        fields = ('rating', 'comment')
        widgets = {
            'comment': forms.Textarea(attrs={'rows': 4, 'placeholder': 'Write your review here...'}),
        }


class CheckoutForm(forms.Form):
    """Form for checkout process"""
    shipping_address = forms.IntegerField(widget=forms.HiddenInput())
    billing_address = forms.IntegerField(widget=forms.HiddenInput())
    payment_method = forms.ChoiceField(
        choices=[
            ('credit_card', 'Credit Card'),
            ('paypal', 'PayPal'),
        ],
        widget=forms.RadioSelect()
    )
