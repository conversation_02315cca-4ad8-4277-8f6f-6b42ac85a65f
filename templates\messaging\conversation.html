{% extends 'base.html' %}

{% block title %}Conversation with {{ other_user.username }} | PetPaw{% endblock %}

{% block extra_css %}
<style>
    .conversation-container {
        display: grid;
        grid-template-columns: 350px 1fr;
        height: calc(100vh - 200px);
        min-height: 600px;
        background-color: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-base);
        overflow: hidden;
    }

    @media (max-width: 992px) {
        .conversation-container {
            grid-template-columns: 1fr;
        }

        .conversation-list {
            display: none;
        }
    }

    .conversation-list {
        border-right: 1px solid var(--gray-200);
        overflow-y: auto;
    }

    .conversation-list-header {
        padding: var(--spacing-base) var(--spacing-xl);
        border-bottom: 1px solid var(--gray-200);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .conversation-list-title {
        font-size: var(--font-lg);
        margin: 0;
    }

    .new-message-button {
        background: none;
        border: none;
        color: var(--primary);
        cursor: pointer;
        font-size: var(--font-xl);
    }

    .search-conversations {
        padding: var(--spacing-base) var(--spacing-xl);
        border-bottom: 1px solid var(--gray-200);
    }

    .search-input {
        width: 100%;
        padding: var(--spacing-sm) var(--spacing-base);
        border: 1px solid var(--gray-300);
        border-radius: var(--radius-full);
        font-size: var(--font-sm);
    }

    .search-input:focus {
        outline: none;
        border-color: var(--primary);
    }

    .conversation-items {
        padding: var(--spacing-base) 0;
    }

    .conversation-item {
        display: flex;
        align-items: center;
        gap: var(--gap-base);
        padding: var(--spacing-base) var(--spacing-xl);
        cursor: pointer;
        transition: var(--transition-base);
    }

    .conversation-item:hover {
        background-color: var(--gray-100);
    }

    .conversation-item.active {
        background-color: var(--primary-light);
    }

    .conversation-avatar {
        width: 50px;
        height: 50px;
        border-radius: var(--radius-full);
        object-fit: cover;
    }

    .conversation-info {
        flex: 1;
        min-width: 0;
    }

    .conversation-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-xs);
    }

    .conversation-name {
        font-weight: var(--fw-medium);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .conversation-time {
        font-size: var(--font-xs);
        color: var(--text-light);
        white-space: nowrap;
    }

    .conversation-preview {
        display: flex;
        align-items: center;
        gap: var(--gap-xs);
        color: var(--text-light);
        font-size: var(--font-sm);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .conversation-status {
        position: relative;
    }

    .unread-badge {
        position: absolute;
        top: -5px;
        right: -5px;
        background-color: var(--primary);
        color: var(--white);
        font-size: 10px;
        width: 18px;
        height: 18px;
        border-radius: var(--radius-full);
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .conversation-detail {
        display: flex;
        flex-direction: column;
        height: 100%;
    }

    .conversation-detail-header {
        padding: var(--spacing-base) var(--spacing-xl);
        border-bottom: 1px solid var(--gray-200);
        display: flex;
        align-items: center;
        gap: var(--gap-base);
    }

    .conversation-detail-avatar {
        width: 40px;
        height: 40px;
        border-radius: var(--radius-full);
        object-fit: cover;
    }

    .conversation-detail-info {
        flex: 1;
    }

    .conversation-detail-name {
        font-weight: var(--fw-medium);
    }

    .conversation-detail-status {
        font-size: var(--font-xs);
        color: var(--text-light);
    }

    .conversation-detail-actions {
        display: flex;
        gap: var(--gap-sm);
    }

    .conversation-action-button {
        background: none;
        border: none;
        color: var(--text-light);
        cursor: pointer;
        font-size: var(--font-lg);
        transition: var(--transition-base);
    }

    .conversation-action-button:hover {
        color: var(--primary);
    }

    .messages-container {
        flex: 1;
        padding: var(--spacing-xl);
        overflow-y: auto;
        display: flex;
        flex-direction: column;
        gap: var(--gap-base);
    }

    .message {
        display: flex;
        max-width: 70%;
    }

    .message.incoming {
        align-self: flex-start;
    }

    .message.outgoing {
        align-self: flex-end;
    }

    .message-avatar {
        width: 36px;
        height: 36px;
        border-radius: var(--radius-full);
        object-fit: cover;
        margin-right: var(--spacing-sm);
    }

    .message-content {
        background-color: var(--gray-100);
        padding: var(--spacing-sm) var(--spacing-base);
        border-radius: var(--radius-lg);
        position: relative;
    }

    .message.outgoing .message-content {
        background-color: var(--primary-light);
        color: var(--primary-dark);
    }

    .message-text {
        margin-bottom: var(--spacing-xs);
        word-wrap: break-word;
    }

    .message-time {
        font-size: var(--font-xs);
        color: var(--text-light);
        text-align: right;
    }

    .message-form-container {
        padding: var(--spacing-base) var(--spacing-xl);
        border-top: 1px solid var(--gray-200);
    }

    .message-form {
        display: flex;
        align-items: center;
        gap: var(--gap-base);
    }

    .message-input {
        flex: 1;
        padding: var(--spacing-sm) var(--spacing-base);
        border: 1px solid var(--gray-300);
        border-radius: var(--radius-full);
        font-size: var(--font-base);
        resize: none;
    }

    .message-input:focus {
        outline: none;
        border-color: var(--primary);
    }

    .message-submit {
        background-color: var(--primary);
        color: var(--white);
        border: none;
        border-radius: var(--radius-full);
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: var(--transition-base);
    }

    .message-submit:hover {
        background-color: var(--primary-dark);
    }

    .message-submit:disabled {
        background-color: var(--gray-300);
        cursor: not-allowed;
    }

    .date-separator {
        display: flex;
        align-items: center;
        justify-content: center;
        margin: var(--spacing-base) 0;
    }

    .date-separator-line {
        flex: 1;
        height: 1px;
        background-color: var(--gray-200);
    }

    .date-separator-text {
        padding: 0 var(--spacing-base);
        font-size: var(--font-xs);
        color: var(--text-light);
    }

    .typing-indicator {
        display: flex;
        align-items: center;
        gap: var(--gap-xs);
        color: var(--text-light);
        font-size: var(--font-xs);
        padding: var(--spacing-xs) 0;
    }

    .typing-dots {
        display: flex;
        gap: 2px;
    }

    .typing-dot {
        width: 6px;
        height: 6px;
        border-radius: var(--radius-full);
        background-color: var(--text-light);
        animation: typingAnimation 1.5s infinite ease-in-out;
    }

    .typing-dot:nth-child(1) {
        animation-delay: 0s;
    }

    .typing-dot:nth-child(2) {
        animation-delay: 0.3s;
    }

    .typing-dot:nth-child(3) {
        animation-delay: 0.6s;
    }

    @keyframes typingAnimation {
        0% {
            transform: translateY(0);
        }
        50% {
            transform: translateY(-5px);
        }
        100% {
            transform: translateY(0);
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="conversation-container">
        <div class="conversation-list">
            <div class="conversation-list-header">
                <h2 class="conversation-list-title">Messages</h2>
                <button type="button" class="new-message-button" id="new-message-btn">
                    <i class="fas fa-edit"></i>
                </button>
            </div>

            <div class="search-conversations">
                <input type="text" placeholder="Search conversations..." class="search-input" id="search-conversations">
            </div>

            <div class="conversation-items">
                {% for conv in conversations %}
                    <a href="{% url 'conversation-detail' pk=conv.id %}" class="conversation-item {% if conversation.id == conv.id %}active{% endif %}">
                        {% if conv.other_user == user %}
                            <img src="{{ conv.creator.profile_picture.url }}" alt="{{ conv.creator.username }}" class="conversation-avatar">
                        {% else %}
                            <img src="{{ conv.other_user.profile_picture.url }}" alt="{{ conv.other_user.username }}" class="conversation-avatar">
                        {% endif %}

                        <div class="conversation-info">
                            <div class="conversation-header">
                                <div class="conversation-name">
                                    {% if conv.other_user == user %}
                                        {{ conv.creator.username }}
                                    {% else %}
                                        {{ conv.other_user.username }}
                                    {% endif %}
                                </div>
                                <div class="conversation-time">{{ conv.last_message.created_at|date:"g:i A" }}</div>
                            </div>

                            <div class="conversation-preview">
                                {% if conv.last_message.sender == user %}
                                    <span>You:</span>
                                {% endif %}
                                {{ conv.last_message.content|truncatechars:30 }}
                            </div>
                        </div>

                        <div class="conversation-status">
                            {% if conv.unread_count > 0 and conv.last_message.sender != user %}
                                <div class="unread-badge">{{ conv.unread_count }}</div>
                            {% endif %}
                        </div>
                    </a>
                {% endfor %}
            </div>
        </div>

        <div class="conversation-detail">
            <div class="conversation-detail-header">
                <img src="{{ other_user.profile_picture.url }}" alt="{{ other_user.username }}" class="conversation-detail-avatar">

                <div class="conversation-detail-info">
                    <div class="conversation-detail-name">{{ other_user.username }}</div>
                    <div class="conversation-detail-status">
                        {% if other_user.is_online %}
                            <span class="text-success">Online</span>
                        {% else %}
                            <span>Last seen {{ other_user.last_login|timesince }} ago</span>
                        {% endif %}
                    </div>
                </div>

                <div class="conversation-detail-actions">
                    <a href="{% url 'user-profile' username=other_user.username %}" class="conversation-action-button" title="View Profile">
                        <i class="fas fa-user"></i>
                    </a>
                    <button type="button" class="conversation-action-button" title="More Options">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                </div>
            </div>

            <div class="messages-container" id="messages-container">
                {% for message in messages %}
                    {% if message.is_date_separator %}
                        <div class="date-separator">
                            <div class="date-separator-line"></div>
                            <div class="date-separator-text">{{ message.date }}</div>
                            <div class="date-separator-line"></div>
                        </div>
                    {% else %}
                        <div class="message {% if message.sender == user %}outgoing{% else %}incoming{% endif %}">
                            {% if message.sender != user %}
                                <img src="{{ message.sender.profile_picture.url }}" alt="{{ message.sender.username }}" class="message-avatar">
                            {% endif %}

                            <div class="message-content">
                                <div class="message-text">{{ message.content }}</div>
                                <div class="message-time">{{ message.created_at|date:"g:i A" }}</div>
                            </div>
                        </div>
                    {% endif %}
                {% endfor %}

                <div class="typing-indicator" id="typing-indicator" style="display: none;">
                    <span>{{ other_user.username }} is typing</span>
                    <div class="typing-dots">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                </div>
            </div>

            <div class="message-form-container">
                <form method="post" action="{% url 'conversation-detail' pk=conversation.id %}" class="message-form" id="message-form">
                    {% csrf_token %}
                    <textarea name="content" id="message-input" class="message-input" placeholder="Type a message..." rows="1" required></textarea>
                    <button type="submit" class="message-submit" id="message-submit">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- New Message Modal -->
    <div class="new-message-modal" id="new-message-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">New Message</h3>
                <button type="button" class="modal-close" id="modal-close-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="modal-body">
                <form id="new-conversation-form">
                    <div class="form-group">
                        <label for="recipient" class="form-label">To:</label>
                        <input type="text" id="recipient" name="recipient" class="form-control" placeholder="Enter username" required>
                        <div id="recipient-suggestions" class="dropdown-menu"></div>
                    </div>

                    <div class="form-group">
                        <label for="message" class="form-label">Message:</label>
                        <textarea id="message" name="message" rows="4" class="form-control" placeholder="Type your message here..." required></textarea>
                    </div>
                </form>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-outline" id="modal-cancel-btn">Cancel</button>
                <button type="button" class="btn btn-primary" id="send-message-btn">Send Message</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Scroll to bottom of messages
        const messagesContainer = document.getElementById('messages-container');
        if (messagesContainer) {
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        // Auto-resize message input
        const messageInput = document.getElementById('message-input');
        if (messageInput) {
            messageInput.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = (this.scrollHeight) + 'px';
            });
        }

        // New Message Modal
        const newMessageBtn = document.getElementById('new-message-btn');
        const newMessageModal = document.getElementById('new-message-modal');
        const modalCloseBtn = document.getElementById('modal-close-btn');
        const modalCancelBtn = document.getElementById('modal-cancel-btn');
        const sendMessageBtn = document.getElementById('send-message-btn');

        function openModal() {
            newMessageModal.classList.add('show');
        }

        function closeModal() {
            newMessageModal.classList.remove('show');
            document.getElementById('new-conversation-form').reset();
        }

        if (newMessageBtn) {
            newMessageBtn.addEventListener('click', openModal);
        }

        if (modalCloseBtn) {
            modalCloseBtn.addEventListener('click', closeModal);
        }

        if (modalCancelBtn) {
            modalCancelBtn.addEventListener('click', closeModal);
        }

        // Close modal when clicking outside
        newMessageModal.addEventListener('click', function(e) {
            if (e.target === newMessageModal) {
                closeModal();
            }
        });

        // Send new message
        if (sendMessageBtn) {
            sendMessageBtn.addEventListener('click', function() {
                const recipient = document.getElementById('recipient').value;
                const message = document.getElementById('message').value;

                if (!recipient || !message) {
                    return;
                }

                // Send message via AJAX
                fetch(`{% url 'start-conversation' username='placeholder' %}`.replace('placeholder', recipient), {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': getCookie('csrftoken'),
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        message: message
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Redirect to the new conversation
                        window.location.href = data.redirect_url;
                    } else {
                        // Show error message
                        console.error('Error:', data.error);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
            });
        }

        // Search conversations
        const searchInput = document.getElementById('search-conversations');
        const conversationItems = document.querySelectorAll('.conversation-item');

        if (searchInput && conversationItems.length) {
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();

                conversationItems.forEach(item => {
                    const name = item.querySelector('.conversation-name').textContent.toLowerCase();
                    const preview = item.querySelector('.conversation-preview').textContent.toLowerCase();

                    if (name.includes(searchTerm) || preview.includes(searchTerm)) {
                        item.style.display = '';
                    } else {
                        item.style.display = 'none';
                    }
                });
            });
        }

        // Message form submission
        const messageForm = document.getElementById('message-form');

        if (messageForm) {
            messageForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const formData = new FormData(this);

                fetch(this.action, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Add new message to the conversation
                        const messageElement = document.createElement('div');
                        messageElement.className = 'message outgoing';
                        messageElement.innerHTML = `
                            <div class="message-content">
                                <div class="message-text">${data.message.content}</div>
                                <div class="message-time">${data.message.time}</div>
                            </div>
                        `;

                        messagesContainer.appendChild(messageElement);
                        messagesContainer.scrollTop = messagesContainer.scrollHeight;

                        // Clear input
                        messageInput.value = '';
                        messageInput.style.height = 'auto';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
            });
        }

        // WebSocket connection for real-time messaging
        const conversationId = {{ conversation.id }};
        const ws_scheme = window.location.protocol === 'https:' ? 'wss' : 'ws';
        const ws_path = `${ws_scheme}://${window.location.host}/ws/messaging/${conversationId}/`;
        const socket = new WebSocket(ws_path);

        socket.onmessage = function(e) {
            const data = JSON.parse(e.data);

            if (data.type === 'message') {
                // Add new message to the conversation
                const messageElement = document.createElement('div');
                messageElement.className = 'message incoming';
                messageElement.innerHTML = `
                    <img src="${data.sender_avatar}" alt="${data.sender_username}" class="message-avatar">
                    <div class="message-content">
                        <div class="message-text">${data.content}</div>
                        <div class="message-time">${data.time}</div>
                    </div>
                `;

                messagesContainer.appendChild(messageElement);
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            } else if (data.type === 'typing') {
                // Show typing indicator
                const typingIndicator = document.getElementById('typing-indicator');

                if (data.is_typing) {
                    typingIndicator.style.display = 'flex';
                } else {
                    typingIndicator.style.display = 'none';
                }
            }
        };

        // Send typing indicator
        let typingTimeout;

        if (messageInput) {
            messageInput.addEventListener('input', function() {
                // Clear previous timeout
                clearTimeout(typingTimeout);

                // Send typing indicator
                socket.send(JSON.stringify({
                    'type': 'typing',
                    'is_typing': true
                }));

                // Set timeout to stop typing indicator
                typingTimeout = setTimeout(function() {
                    socket.send(JSON.stringify({
                        'type': 'typing',
                        'is_typing': false
                    }));
                }, 3000);
            });
        }

        // Helper function to get CSRF token
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
    });
</script>
{% endblock %}
