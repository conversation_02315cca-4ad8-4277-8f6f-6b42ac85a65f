{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}Service Provider Dashboard - PetPaw{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3">
            <div class="card shadow-sm mb-4">
                <div class="card-body text-center">
                    {% if user.service_provider.profile_picture %}
                        <img src="{{ user.service_provider.profile_picture.url }}" alt="{{ user.username }}" class="rounded-circle img-fluid mb-3" style="width: 120px; height: 120px; object-fit: cover;">
                    {% else %}
                        <img src="/static/img/default-profile.png" alt="{{ user.username }}" class="rounded-circle img-fluid mb-3" style="width: 120px; height: 120px; object-fit: cover;">
                    {% endif %}
                    <h5 class="mb-0">{{ user.get_full_name|default:user.username }}</h5>
                    <p class="text-muted">Service Provider</p>
                    <div class="d-grid gap-2">
                        <a href="{% url 'provider-detail' user.service_provider.id %}" class="btn btn-outline-primary btn-sm">View Public Profile</a>
                    </div>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{% url 'provider-dashboard' %}" class="list-group-item list-group-item-action {% if active_tab == 'dashboard' %}active{% endif %}">
                        <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                    </a>
                    <a href="{% url 'provider-services' %}" class="list-group-item list-group-item-action {% if active_tab == 'services' %}active{% endif %}">
                        <i class="fas fa-concierge-bell me-2"></i> My Services
                    </a>
                    <a href="{% url 'provider-bookings' %}" class="list-group-item list-group-item-action {% if active_tab == 'bookings' %}active{% endif %}">
                        <i class="fas fa-calendar-check me-2"></i> Bookings
                    </a>
                    <a href="{% url 'provider-availability' %}" class="list-group-item list-group-item-action {% if active_tab == 'availability' %}active{% endif %}">
                        <i class="fas fa-clock me-2"></i> Availability
                    </a>
                    <a href="{% url 'provider-settings' %}" class="list-group-item list-group-item-action {% if active_tab == 'settings' %}active{% endif %}">
                        <i class="fas fa-cog me-2"></i> Settings
                    </a>
                </div>
            </div>
            <div class="card shadow-sm">
                <div class="card-body">
                    <h6 class="card-title">Provider Status</h6>
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="availabilityToggle" {% if user.service_provider.is_available %}checked{% endif %}>
                        <label class="form-check-label" for="availabilityToggle">Available for Bookings</label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9">
            <!-- Dashboard Overview -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Dashboard Overview</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card bg-light mb-3">
                                <div class="card-body text-center">
                                    <h3 class="mb-0">{{ services_count }}</h3>
                                    <p class="text-muted mb-0">Services</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light mb-3">
                                <div class="card-body text-center">
                                    <h3 class="mb-0">{{ pending_bookings_count }}</h3>
                                    <p class="text-muted mb-0">Pending Bookings</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light mb-3">
                                <div class="card-body text-center">
                                    <h3 class="mb-0">{{ completed_bookings_count }}</h3>
                                    <p class="text-muted mb-0">Completed Bookings</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Bookings -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Recent Bookings</h5>
                    <a href="{% url 'provider-bookings' %}" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body">
                    {% if recent_bookings %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Client</th>
                                        <th>Service</th>
                                        <th>Date</th>
                                        <th>Status</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for booking in recent_bookings %}
                                    <tr>
                                        <td>{{ booking.user.get_full_name|default:booking.user.username }}</td>
                                        <td>{{ booking.service.name }}</td>
                                        <td>{{ booking.date|date:"M d, Y" }} at {{ booking.start_time|time:"g:i A" }}</td>
                                        <td>
                                            {% if booking.status == 'pending' %}
                                                <span class="badge bg-warning text-dark">Pending</span>
                                            {% elif booking.status == 'confirmed' %}
                                                <span class="badge bg-success">Confirmed</span>
                                            {% elif booking.status == 'completed' %}
                                                <span class="badge bg-info">Completed</span>
                                            {% elif booking.status == 'cancelled' %}
                                                <span class="badge bg-danger">Cancelled</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <a href="{% url 'booking-detail' booking.id %}" class="btn btn-sm btn-outline-primary">View</a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-muted text-center my-4">No recent bookings found.</p>
                    {% endif %}
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <a href="{% url 'add-service' %}" class="btn btn-outline-primary d-block mb-3">
                                <i class="fas fa-plus-circle me-2"></i> Add New Service
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="{% url 'add-availability' %}" class="btn btn-outline-primary d-block mb-3">
                                <i class="fas fa-clock me-2"></i> Add Availability
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="{% url 'provider-settings' %}" class="btn btn-outline-primary d-block mb-3">
                                <i class="fas fa-user-edit me-2"></i> Update Profile
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Toggle availability status
    document.getElementById('availabilityToggle').addEventListener('change', function() {
        fetch('{% url "provider-toggle-availability" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token }}'
            },
            body: JSON.stringify({
                is_available: this.checked
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message
                console.log('Availability status updated successfully!');
            } else {
                // Show error message and revert toggle
                console.error('Failed to update availability status.');
                this.checked = !this.checked;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            console.error('An error occurred. Please try again.');
            this.checked = !this.checked;
        });
    });
</script>
{% endblock %}
