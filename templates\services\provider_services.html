{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}My Services - PetPaw{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3">
            <div class="card shadow-sm mb-4">
                <div class="card-body text-center">
                    {% if user.service_provider.profile_picture %}
                        <img src="{{ user.service_provider.profile_picture.url }}" alt="{{ user.username }}" class="rounded-circle img-fluid mb-3" style="width: 120px; height: 120px; object-fit: cover;">
                    {% else %}
                        <img src="/static/img/default-profile.png" alt="{{ user.username }}" class="rounded-circle img-fluid mb-3" style="width: 120px; height: 120px; object-fit: cover;">
                    {% endif %}
                    <h5 class="mb-0">{{ user.get_full_name|default:user.username }}</h5>
                    <p class="text-muted">Service Provider</p>
                    <div class="d-grid gap-2">
                        <a href="{% url 'provider-detail' user.service_provider.id %}" class="btn btn-outline-primary btn-sm">View Public Profile</a>
                    </div>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{% url 'provider-dashboard' %}" class="list-group-item list-group-item-action {% if active_tab == 'dashboard' %}active{% endif %}">
                        <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                    </a>
                    <a href="{% url 'provider-services' %}" class="list-group-item list-group-item-action {% if active_tab == 'services' %}active{% endif %}">
                        <i class="fas fa-concierge-bell me-2"></i> My Services
                    </a>
                    <a href="{% url 'provider-bookings' %}" class="list-group-item list-group-item-action {% if active_tab == 'bookings' %}active{% endif %}">
                        <i class="fas fa-calendar-check me-2"></i> Bookings
                    </a>
                    <a href="{% url 'provider-availability' %}" class="list-group-item list-group-item-action {% if active_tab == 'availability' %}active{% endif %}">
                        <i class="fas fa-clock me-2"></i> Availability
                    </a>
                    <a href="{% url 'provider-settings' %}" class="list-group-item list-group-item-action {% if active_tab == 'settings' %}active{% endif %}">
                        <i class="fas fa-cog me-2"></i> Settings
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-md-9">
            <!-- Services Header -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">My Services</h5>
                    <a href="{% url 'add-service' %}" class="btn btn-primary">
                        <i class="fas fa-plus-circle me-2"></i> Add New Service
                    </a>
                </div>
                <div class="card-body">
                    {% if services %}
                        <div class="row">
                            {% for service in services %}
                                <div class="col-md-6 mb-4">
                                    <div class="card h-100">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start mb-3">
                                                <h5 class="card-title mb-0">{{ service.name }}</h5>
                                                <span class="badge {% if service.is_available %}bg-success{% else %}bg-danger{% endif %}">
                                                    {% if service.is_available %}Available{% else %}Unavailable{% endif %}
                                                </span>
                                            </div>
                                            <h6 class="text-muted">{{ service.category.name }}</h6>
                                            <p class="card-text">{{ service.description|truncatechars:100 }}</p>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <h5 class="mb-0">${{ service.price }}</h5>
                                                <span class="text-muted">{{ service.duration }} min</span>
                                            </div>
                                        </div>
                                        <div class="card-footer bg-white d-flex justify-content-between">
                                            <a href="{% url 'update-service' service.id %}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-edit me-1"></i> Edit
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteModal{{ service.id }}">
                                                <i class="fas fa-trash-alt me-1"></i> Delete
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Delete Modal -->
                                <div class="modal fade" id="deleteModal{{ service.id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ service.id }}" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="deleteModalLabel{{ service.id }}">Confirm Delete</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <p>Are you sure you want to delete the service <strong>{{ service.name }}</strong>?</p>
                                                <p class="text-danger">This action cannot be undone.</p>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                <form action="{% url 'delete-service' service.id %}" method="post">
                                                    {% csrf_token %}
                                                    <button type="submit" class="btn btn-danger">Delete</button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-concierge-bell fa-3x text-muted mb-3"></i>
                            <h5>No Services Added Yet</h5>
                            <p class="text-muted">Start by adding your first service to attract clients.</p>
                            <a href="{% url 'add-service' %}" class="btn btn-primary mt-2">
                                <i class="fas fa-plus-circle me-2"></i> Add New Service
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
