from django.db import models
from django.urls import reverse
from users.models import User
from pets.models import Pet


class Post(models.Model):
    """Model for social media posts"""
    POST_TYPES = (
        ('image', 'Image Post'),
        ('video', 'Video Post'),
    )
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='posts')
    pet = models.ForeignKey(Pet, on_delete=models.CASCADE, related_name='posts', null=True, blank=True)
    content = models.TextField()
    post_type = models.CharField(max_length=10, choices=POST_TYPES, default='image')
    image = models.ImageField(upload_to='post_images', blank=True, null=True)
    video = models.FileField(upload_to='post_videos', blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    likes = models.ManyToManyField(User, related_name='liked_posts', blank=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Post by {self.user.username} on {self.created_at.strftime('%Y-%m-%d')}"
    
    def get_absolute_url(self):
        return reverse('post-detail', kwargs={'pk': self.pk})
    
    def get_like_count(self):
        return self.likes.count()
    
    def get_comment_count(self):
        return self.comments.count()


class Comment(models.Model):
    """Model for comments on posts"""
    post = models.ForeignKey(Post, on_delete=models.CASCADE, related_name='comments')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='comments')
    content = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['created_at']
    
    def __str__(self):
        return f"Comment by {self.user.username} on {self.post}"


class Story(models.Model):
    """Model for temporary stories (disappear after 24 hours)"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='stories')
    pet = models.ForeignKey(Pet, on_delete=models.CASCADE, related_name='stories', null=True, blank=True)
    image = models.ImageField(upload_to='story_images', blank=True, null=True)
    video = models.FileField(upload_to='story_videos', blank=True, null=True)
    caption = models.CharField(max_length=255, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()
    
    class Meta:
        verbose_name_plural = 'Stories'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Story by {self.user.username} on {self.created_at.strftime('%Y-%m-%d %H:%M')}"
    
    def is_expired(self):
        from django.utils import timezone
        return timezone.now() > self.expires_at


class Notification(models.Model):
    """Model for user notifications"""
    NOTIFICATION_TYPES = (
        ('like', 'Like'),
        ('comment', 'Comment'),
        ('follow', 'Follow'),
        ('mention', 'Mention'),
    )
    
    recipient = models.ForeignKey(User, on_delete=models.CASCADE, related_name='notifications')
    sender = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sent_notifications')
    notification_type = models.CharField(max_length=10, choices=NOTIFICATION_TYPES)
    post = models.ForeignKey(Post, on_delete=models.CASCADE, null=True, blank=True)
    comment = models.ForeignKey(Comment, on_delete=models.CASCADE, null=True, blank=True)
    message = models.CharField(max_length=255)
    is_read = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Notification for {self.recipient.username} from {self.sender.username}"


class Hashtag(models.Model):
    """Model for hashtags"""
    name = models.CharField(max_length=50, unique=True)
    posts = models.ManyToManyField(Post, related_name='hashtags', blank=True)
    
    def __str__(self):
        return f"#{self.name}"
    
    def get_absolute_url(self):
        return reverse('hashtag-posts', kwargs={'name': self.name})
    
    def get_post_count(self):
        return self.posts.count()
