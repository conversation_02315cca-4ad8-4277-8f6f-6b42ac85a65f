# Generated by Django 4.2.7 on 2025-05-24 07:46

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('messaging', '0002_initial'),
        ('social', '0002_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='notification',
            name='conversation',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='messaging.conversation'),
        ),
        migrations.AlterField(
            model_name='notification',
            name='notification_type',
            field=models.CharField(choices=[('like', 'Like'), ('comment', 'Comment'), ('follow', 'Follow'), ('mention', 'Mention'), ('message', 'Message')], max_length=10),
        ),
    ]
