from django.db import models
from django.urls import reverse
from users.models import User


class PetCategory(models.Model):
    """Model for pet categories (dog, cat, bird, etc.)"""
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    icon = models.ImageField(upload_to='category_icons', blank=True)
    
    class Meta:
        verbose_name_plural = 'Pet Categories'
    
    def __str__(self):
        return self.name


class PetBreed(models.Model):
    """Model for pet breeds"""
    category = models.ForeignKey(PetCategory, on_delete=models.CASCADE, related_name='breeds')
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    average_lifespan = models.CharField(max_length=50, blank=True)
    size = models.CharField(max_length=50, blank=True)
    temperament = models.CharField(max_length=255, blank=True)
    
    def __str__(self):
        return f"{self.name} ({self.category.name})"


class Pet(models.Model):
    """Model for pets"""
    GENDER_CHOICES = (
        ('M', 'Male'),
        ('F', 'Female'),
        ('U', 'Unknown'),
    )
    
    owner = models.ForeignKey(User, on_delete=models.CASCADE, related_name='pets')
    name = models.CharField(max_length=100)
    category = models.ForeignKey(PetCategory, on_delete=models.CASCADE, related_name='pets')
    breed = models.ForeignKey(PetBreed, on_delete=models.SET_NULL, null=True, blank=True, related_name='pets')
    birth_date = models.DateField(null=True, blank=True)
    gender = models.CharField(max_length=1, choices=GENDER_CHOICES, default='U')
    profile_picture = models.ImageField(upload_to='pet_profiles', default='default_pet.jpg')
    bio = models.TextField(blank=True)
    is_for_adoption = models.BooleanField(default=False)
    adoption_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    followers = models.ManyToManyField(User, related_name='followed_pets', blank=True)
    
    def __str__(self):
        return f"{self.name} ({self.owner.username}'s {self.category.name})"
    
    def get_absolute_url(self):
        return reverse('pet-detail', kwargs={'pk': self.pk})
    
    def get_age(self):
        """Calculate pet's age based on birth date"""
        if not self.birth_date:
            return "Unknown"
        
        from datetime import date
        today = date.today()
        age = today.year - self.birth_date.year - ((today.month, today.day) < (self.birth_date.month, self.birth_date.day))
        
        if age < 1:
            # Calculate months
            months = today.month - self.birth_date.month
            if today.day < self.birth_date.day:
                months -= 1
            if months < 0:
                months += 12
            return f"{months} months"
        else:
            return f"{age} years"


class PetGallery(models.Model):
    """Model for pet photo gallery"""
    pet = models.ForeignKey(Pet, on_delete=models.CASCADE, related_name='gallery')
    image = models.ImageField(upload_to='pet_gallery')
    caption = models.CharField(max_length=255, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        verbose_name_plural = 'Pet Galleries'
    
    def __str__(self):
        return f"Photo of {self.pet.name}"


class PetMedicalRecord(models.Model):
    """Model for pet medical records"""
    pet = models.ForeignKey(Pet, on_delete=models.CASCADE, related_name='medical_records')
    record_date = models.DateField()
    record_type = models.CharField(max_length=100)  # vaccination, checkup, treatment, etc.
    description = models.TextField()
    veterinarian = models.CharField(max_length=255, blank=True)
    clinic = models.CharField(max_length=255, blank=True)
    document = models.FileField(upload_to='pet_medical_records', blank=True)
    
    def __str__(self):
        return f"{self.pet.name}'s {self.record_type} on {self.record_date}"
