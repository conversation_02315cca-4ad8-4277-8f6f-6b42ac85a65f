// Main JavaScript file for PetPaw

document.addEventListener('DOMContentLoaded', function() {
    // Mobile menu toggle
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const mobileMenu = document.querySelector('.mobile-menu');
    const mobileMenuClose = document.querySelector('.mobile-menu-close');

    if (mobileMenuToggle && mobileMenu && mobileMenuClose) {
        mobileMenuToggle.addEventListener('click', function() {
            mobileMenu.classList.add('show');
            document.body.style.overflow = 'hidden';
        });

        mobileMenuClose.addEventListener('click', function() {
            mobileMenu.classList.remove('show');
            document.body.style.overflow = '';
        });
    }

    // User dropdown toggle
    const dropdownToggle = document.querySelector('.dropdown-toggle');
    const dropdownMenu = document.querySelector('.dropdown-menu');

    if (dropdownToggle && dropdownMenu) {
        dropdownToggle.addEventListener('click', function(e) {
            e.stopPropagation();
            dropdownMenu.classList.toggle('show');
        });

        document.addEventListener('click', function(e) {
            if (!dropdownToggle.contains(e.target) && !dropdownMenu.contains(e.target)) {
                dropdownMenu.classList.remove('show');
            }
        });
    }

    // Django message close button
    const messageCloseButtons = document.querySelectorAll('.django-message-close');

    if (messageCloseButtons.length > 0) {
        messageCloseButtons.forEach(button => {
            button.addEventListener('click', function() {
                const message = this.closest('.django-message');
                message.style.opacity = '0';
                setTimeout(() => {
                    message.remove();
                }, 300);
            });
        });

        // Auto-hide Django messages after 5 seconds
        setTimeout(() => {
            document.querySelectorAll('.django-message').forEach(message => {
                message.style.opacity = '0';
                setTimeout(() => {
                    message.remove();
                }, 300);
            });
        }, 5000);
    }

    // Product quantity increment/decrement
    const quantityInputs = document.querySelectorAll('.quantity-input');

    if (quantityInputs.length > 0) {
        quantityInputs.forEach(input => {
            const decrementBtn = input.previousElementSibling;
            const incrementBtn = input.nextElementSibling;

            decrementBtn.addEventListener('click', function() {
                let value = parseInt(input.value);
                if (value > 1) {
                    input.value = value - 1;

                    // Trigger change event for any listeners
                    const event = new Event('change', { bubbles: true });
                    input.dispatchEvent(event);
                }
            });

            incrementBtn.addEventListener('click', function() {
                let value = parseInt(input.value);
                input.value = value + 1;

                // Trigger change event for any listeners
                const event = new Event('change', { bubbles: true });
                input.dispatchEvent(event);
            });
        });
    }

    // Cart item quantity update
    const cartQuantityForms = document.querySelectorAll('.cart-item-quantity-form');

    if (cartQuantityForms.length > 0) {
        cartQuantityForms.forEach(form => {
            const input = form.querySelector('.quantity-input');

            input.addEventListener('change', function() {
                form.submit();
            });
        });
    }

    // Add to cart AJAX
    const addToCartButtons = document.querySelectorAll('.add-to-cart-btn');

    if (addToCartButtons.length > 0) {
        addToCartButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();

                const url = this.getAttribute('data-url');

                fetch(url, {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRFToken': getCookie('csrftoken')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update cart count
                        const cartCount = document.querySelector('.cart-count');
                        if (cartCount) {
                            cartCount.textContent = data.cart_total;
                        }

                        // Show success message
                        showMessage('Product added to cart successfully!', 'success');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showMessage('Failed to add product to cart.', 'error');
                });
            });
        });
    }

    // Helper function to get CSRF token
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    // Helper function to show messages - DISABLED to prevent popup notifications
    function showMessage(text, type) {
        // Do nothing - we don't want popup messages for messaging system
        console.log('Message notification (disabled):', text, type);
        return;
    }
});
