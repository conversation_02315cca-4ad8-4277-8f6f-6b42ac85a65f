"""
URL configuration for petpaw project.
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.views.generic import TemplateView
from users import views as user_views

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', TemplateView.as_view(template_name='home.html'), name='home'),
    path('accounts/', include('allauth.urls')),
    # Add a direct URL pattern for the edit profile page
    path('users/profile/edit/', user_views.edit_profile, name='edit-profile-direct'),
    path('users/', include('users.urls')),
    path('pets/', include('pets.urls')),
    path('shop/', include('shop.urls')),
    path('services/', include('services.urls')),
    path('social/', include('social.urls')),
    path('messages/', include('messaging.urls')),
    # Example pages
    path('examples/inputs/', TemplateView.as_view(template_name='examples/input_examples.html'), name='input-examples'),
    path('examples/buttons/', TemplateView.as_view(template_name='examples/button_examples.html'), name='button-examples'),
]

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
