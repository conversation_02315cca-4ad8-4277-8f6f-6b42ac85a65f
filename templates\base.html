<!DOCTYPE html>
{% load static %}
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="notification" content="disabled">
    <meta name="push-notification" content="disabled">
    <title>{% block title %}PetPaw - A World for Pets and Their Parents{% endblock %}</title>

    <!-- Favicon -->
    <link rel="shortcut icon" href="{% static 'img/favicon.ico' %}" type="image/x-icon">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/variables.css' %}">
    <link rel="stylesheet" href="{% static 'css/base.css' %}">
    <link rel="stylesheet" href="{% static 'css/components.css' %}">
    <link rel="stylesheet" href="{% static 'css/navigation.css' %}">
    <link rel="stylesheet" href="{% static 'css/responsive.css' %}">

    <!-- Page-specific CSS -->
    {% block extra_css %}{% endblock %}

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Include the navigation header -->
    {% include 'includes/navigation.html' %}

    {% if messages %}
        <div class="messages-container">
            {% for message in messages %}
                <div class="message {{ message.tags }}">
                    <div class="message-content">
                        {{ message }}
                    </div>
                    <button class="message-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <main class="main-content">
        {% block content %}{% endblock %}
    </main>

    <footer class="main-footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <img src="{% static 'img/logo.svg' %}" alt="PetPaw Logo">
                    <h3>PetPaw</h3>
                    <p>A world for pets and their parents</p>
                </div>

                <div class="footer-links">
                    <div class="footer-section">
                        <h4>Explore</h4>
                        <ul>
                            <li><a href="{% url 'home' %}">Home</a></li>
                            <li><a href="{% url 'pet-list' %}">Pets</a></li>
                            <li><a href="{% url 'product-list' %}">Shop</a></li>
                            <li><a href="{% url 'provider-list' %}">Services</a></li>
                            <li><a href="{% url 'feed' %}">Feed</a></li>
                        </ul>
                    </div>

                    <div class="footer-section">
                        <h4>Information</h4>
                        <ul>
                            <li><a href="#">About Us</a></li>
                            <li><a href="#">Contact Us</a></li>
                            <li><a href="#">Privacy Policy</a></li>
                            <li><a href="#">Terms of Service</a></li>
                            <li><a href="#">FAQ</a></li>
                        </ul>
                    </div>

                    <div class="footer-section">
                        <h4>Connect</h4>
                        <div class="social-links">
                            <a href="#"><i class="fab fa-facebook-f"></i></a>
                            <a href="#"><i class="fab fa-twitter"></i></a>
                            <a href="#"><i class="fab fa-instagram"></i></a>
                            <a href="#"><i class="fab fa-youtube"></i></a>
                        </div>
                        <div class="newsletter">
                            <h5>Subscribe to our newsletter</h5>
                            <form action="#" method="POST">
                                <input type="email" placeholder="Your email address">
                                <button type="submit"><i class="fas fa-paper-plane"></i></button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; {% now "Y" %} PetPaw. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- COMPLETELY DISABLE ALL BROWSER NOTIFICATIONS -->
    <script>
    // NUCLEAR OPTION: COMPLETELY DISABLE ALL BROWSER NOTIFICATIONS
    (function() {
        'use strict';

        console.log('🚫 INITIALIZING COMPLETE NOTIFICATION BLOCKING');

        // Block all notification APIs immediately
        if ('Notification' in window) {
            // Delete the Notification constructor entirely
            delete window.Notification;

            // Create a dummy constructor that does nothing
            window.Notification = function() {
                console.log('🚫 Browser notification COMPLETELY BLOCKED');
                return {
                    close: function() {},
                    onclick: null,
                    onclose: null,
                    onerror: null,
                    onshow: null
                };
            };

            // Make permission always denied
            Object.defineProperty(window.Notification, 'permission', {
                get: function() { return 'denied'; },
                set: function() { return 'denied'; },
                configurable: false,
                enumerable: true
            });

            // Block requestPermission
            window.Notification.requestPermission = function() {
                console.log('🚫 Notification permission request COMPLETELY BLOCKED');
                return Promise.resolve('denied');
            };
        }

        // Block push notifications
        if ('PushManager' in window) {
            delete window.PushManager;
        }

        // Block service worker notifications
        if (navigator.serviceWorker) {
            const originalRegister = navigator.serviceWorker.register;
            navigator.serviceWorker.register = function() {
                console.log('🚫 Service Worker registration BLOCKED');
                return Promise.reject(new Error('Service Worker blocked'));
            };

            // Block existing service workers
            navigator.serviceWorker.getRegistrations().then(function(registrations) {
                registrations.forEach(function(registration) {
                    if (registration.showNotification) {
                        registration.showNotification = function() {
                            console.log('🚫 Service Worker notification BLOCKED');
                            return Promise.resolve();
                        };
                    }
                });
            }).catch(function() {
                // Ignore errors
            });
        }

        // Block any other notification methods
        if (window.webkitNotifications) {
            delete window.webkitNotifications;
        }

        // Block desktop notifications
        if (window.external && window.external.AddFavorite) {
            const original = window.external.AddFavorite;
            window.external.AddFavorite = function() {
                console.log('🚫 External notification BLOCKED');
            };
        }

        // Block alert() calls that might trigger notifications
        window.alert = function(message) {
            console.log('🚫 Alert blocked:', message);
        };

        // Block confirm() calls
        window.confirm = function(message) {
            console.log('🚫 Confirm blocked:', message);
            return false;
        };

        // Block prompt() calls
        window.prompt = function(message) {
            console.log('🚫 Prompt blocked:', message);
            return null;
        };

        console.log('🚫 NOTIFICATION BLOCKING COMPLETE');
    })();
    </script>

    <!-- Custom JS -->
    <script src="{% static 'js/main.js' %}"></script>

    <!-- Page-specific JS -->
    {% block extra_js %}{% endblock %}
</body>
</html>
