{% extends 'base.html' %}

{% block title %}Add Medical Record for {{ pet.name }} | PetPaw{% endblock %}

{% block extra_css %}
<style>
    .medical-form-container {
        max-width: 800px;
        margin: 0 auto;
        padding: var(--spacing-xl) 0;
    }
    
    .medical-form-header {
        margin-bottom: var(--spacing-2xl);
        text-align: center;
    }
    
    .medical-form {
        background-color: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-base);
        padding: var(--spacing-2xl);
    }
    
    .form-section {
        margin-bottom: var(--spacing-2xl);
    }
    
    .form-section-title {
        font-size: var(--font-lg);
        margin-bottom: var(--spacing-lg);
        padding-bottom: var(--spacing-sm);
        border-bottom: 1px solid var(--gray-200);
    }
    
    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--gap-lg);
        margin-bottom: var(--spacing-lg);
    }
    
    @media (max-width: 768px) {
        .form-row {
            grid-template-columns: 1fr;
        }
    }
    
    .file-upload-container {
        border: 2px dashed var(--gray-300);
        border-radius: var(--radius-md);
        padding: var(--spacing-lg);
        text-align: center;
        margin-bottom: var(--spacing-lg);
        cursor: pointer;
        transition: var(--transition-base);
    }
    
    .file-upload-container:hover {
        border-color: var(--primary);
    }
    
    .file-upload-icon {
        font-size: var(--font-2xl);
        color: var(--gray-400);
        margin-bottom: var(--spacing-sm);
    }
    
    .file-upload-text {
        margin-bottom: var(--spacing-xs);
    }
    
    .file-upload-info {
        font-size: var(--font-xs);
        color: var(--text-light);
    }
    
    .form-actions {
        display: flex;
        justify-content: flex-end;
        gap: var(--gap-base);
        margin-top: var(--spacing-xl);
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="medical-form-container">
        <div class="medical-form-header">
            <h1>Add Medical Record for {{ pet.name }}</h1>
            <p>Keep track of your pet's health history</p>
        </div>
        
        <div class="medical-form">
            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}
                
                <div class="form-section">
                    <h2 class="form-section-title">Record Information</h2>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="{{ form.record_date.id_for_label }}" class="form-label">Date</label>
                            {{ form.record_date }}
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ form.record_type.id_for_label }}" class="form-label">Record Type</label>
                            {{ form.record_type }}
                            <small class="form-text">E.g., Vaccination, Check-up, Treatment, etc.</small>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="{{ form.description.id_for_label }}" class="form-label">Description</label>
                        {{ form.description }}
                    </div>
                </div>
                
                <div class="form-section">
                    <h2 class="form-section-title">Veterinary Information</h2>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="{{ form.veterinarian.id_for_label }}" class="form-label">Veterinarian</label>
                            {{ form.veterinarian }}
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ form.clinic.id_for_label }}" class="form-label">Clinic/Hospital</label>
                            {{ form.clinic }}
                        </div>
                    </div>
                </div>
                
                <div class="form-section">
                    <h2 class="form-section-title">Attachments</h2>
                    
                    <div class="file-upload-container" id="document-upload-container">
                        <div class="file-upload-icon">
                            <i class="far fa-file-alt"></i>
                        </div>
                        <div class="file-upload-text">Click to upload a document (optional)</div>
                        <div class="file-upload-info">PDF, DOC, or JPG • Max 10MB</div>
                        <div style="display:none;">{{ form.document }}</div>
                    </div>
                    
                    <div id="file-name-display" class="form-text"></div>
                </div>
                
                <div class="form-actions">
                    <a href="{% url 'pet-detail' pk=pet.pk %}" class="btn btn-outline">Cancel</a>
                    <button type="submit" class="btn btn-primary">Add Record</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Document upload handling
        const documentUploadContainer = document.getElementById('document-upload-container');
        const documentInput = document.querySelector('input[name="document"]');
        const fileNameDisplay = document.getElementById('file-name-display');
        
        documentUploadContainer.addEventListener('click', function() {
            documentInput.click();
        });
        
        documentInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                fileNameDisplay.textContent = `Selected file: ${this.files[0].name}`;
            }
        });
    });
</script>
{% endblock %}
