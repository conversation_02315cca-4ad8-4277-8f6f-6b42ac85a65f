{% extends 'base.html' %}
{% load static %}

{% block title %}My Bookings - PetPaw{% endblock %}

{% block content %}
<div class="container">
    <div class="page-header">
        <h1>My Bookings</h1>
        <p>View and manage your service bookings</p>
    </div>

    {% if bookings %}
        <div class="bookings-grid">
            {% for booking in bookings %}
                <div class="booking-card">
                    <div class="booking-header">
                        <div class="service-info">
                            <h3>{{ booking.service.name }}</h3>
                            <p class="provider-name">
                                <i class="fas fa-user"></i>
                                {{ booking.service.provider.user.get_full_name|default:booking.service.provider.user.username }}
                            </p>
                        </div>
                        <div class="booking-status">
                            <span class="status-badge status-{{ booking.status }}">
                                {{ booking.get_status_display }}
                            </span>
                        </div>
                    </div>

                    <div class="booking-details">
                        <div class="detail-item">
                            <i class="fas fa-calendar"></i>
                            <span>{{ booking.date|date:"F d, Y" }}</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-clock"></i>
                            <span>{{ booking.start_time|time:"g:i A" }} - {{ booking.end_time|time:"g:i A" }}</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-dollar-sign"></i>
                            <span>${{ booking.total_price }}</span>
                        </div>
                        {% if booking.pet %}
                            <div class="detail-item">
                                <i class="fas fa-paw"></i>
                                <span>{{ booking.pet.name }}</span>
                            </div>
                        {% endif %}
                    </div>

                    {% if booking.notes %}
                        <div class="booking-notes">
                            <h4>Notes:</h4>
                            <p>{{ booking.notes }}</p>
                        </div>
                    {% endif %}

                    <div class="booking-actions">
                        <a href="{% url 'booking-detail' booking.pk %}" class="btn btn-primary">
                            <i class="fas fa-eye"></i>
                            View Details
                        </a>
                        
                        {% if booking.status == 'pending' %}
                            <button class="btn btn-secondary" onclick="cancelBooking({{ booking.pk }})">
                                <i class="fas fa-times"></i>
                                Cancel
                            </button>
                        {% endif %}
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="empty-state">
            <div class="empty-icon">
                <i class="fas fa-calendar-times"></i>
            </div>
            <h3>No Bookings Yet</h3>
            <p>You haven't made any service bookings yet. Browse our services to get started!</p>
            <a href="{% url 'provider-list' %}" class="btn btn-primary">
                <i class="fas fa-search"></i>
                Browse Services
            </a>
        </div>
    {% endif %}
</div>

<style>
.page-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-xl) 0;
    border-bottom: 1px solid var(--gray-200);
}

.page-header h1 {
    color: var(--primary);
    margin-bottom: var(--spacing-sm);
}

.page-header p {
    color: var(--gray-600);
    font-size: var(--font-lg);
}

.bookings-grid {
    display: grid;
    gap: var(--spacing-lg);
    max-width: 800px;
    margin: 0 auto;
}

.booking-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    transition: var(--transition-base);
}

.booking-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.booking-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: var(--spacing-lg);
    background: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
}

.service-info h3 {
    color: var(--primary);
    margin-bottom: var(--spacing-xs);
    font-size: var(--font-xl);
}

.provider-name {
    color: var(--gray-600);
    font-size: var(--font-sm);
    margin: 0;
}

.provider-name i {
    margin-right: var(--spacing-xs);
}

.status-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-full);
    font-size: var(--font-sm);
    font-weight: 600;
    text-transform: uppercase;
}

.status-pending {
    background: var(--warning-light);
    color: var(--warning-dark);
}

.status-confirmed {
    background: var(--info-light);
    color: var(--info-dark);
}

.status-completed {
    background: var(--success-light);
    color: var(--success-dark);
}

.status-cancelled {
    background: var(--danger-light);
    color: var(--danger-dark);
}

.booking-details {
    padding: var(--spacing-lg);
    display: grid;
    gap: var(--spacing-sm);
}

.detail-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--gray-700);
}

.detail-item i {
    width: 16px;
    color: var(--primary);
}

.booking-notes {
    padding: 0 var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.booking-notes h4 {
    color: var(--gray-800);
    margin-bottom: var(--spacing-xs);
    font-size: var(--font-base);
}

.booking-notes p {
    color: var(--gray-600);
    font-size: var(--font-sm);
    line-height: 1.5;
    margin: 0;
}

.booking-actions {
    padding: var(--spacing-lg);
    background: var(--gray-50);
    border-top: 1px solid var(--gray-200);
    display: flex;
    gap: var(--spacing-sm);
}

.empty-state {
    text-align: center;
    padding: var(--spacing-4xl) var(--spacing-lg);
    max-width: 500px;
    margin: 0 auto;
}

.empty-icon {
    font-size: 4rem;
    color: var(--gray-400);
    margin-bottom: var(--spacing-lg);
}

.empty-state h3 {
    color: var(--gray-800);
    margin-bottom: var(--spacing-sm);
}

.empty-state p {
    color: var(--gray-600);
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
}

@media (max-width: 768px) {
    .booking-header {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: flex-start;
    }
    
    .booking-actions {
        flex-direction: column;
    }
    
    .booking-actions .btn {
        width: 100%;
        justify-content: center;
    }
}
</style>

<script>
function cancelBooking(bookingId) {
    if (confirm('Are you sure you want to cancel this booking?')) {
        // Add AJAX call to cancel booking
        fetch(`/services/bookings/${bookingId}/cancel/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCookie('csrftoken'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Failed to cancel booking. Please try again.');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to cancel booking. Please try again.');
        });
    }
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
</script>
{% endblock %}
