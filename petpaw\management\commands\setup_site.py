from django.contrib.sites.models import Site
from django.core.management.base import BaseCommand

class Command(BaseCommand):
    help = 'Set up the Site object for django-allauth'

    def handle(self, *args, **options):
        # Check if Site with ID 1 exists
        try:
            site = Site.objects.get(id=1)
            self.stdout.write(self.style.SUCCESS(f'Site already exists: {site.domain}'))
        except Site.DoesNotExist:
            # Create the Site object
            site = Site.objects.create(id=1, domain='example.com', name='PetPaw')
            self.stdout.write(self.style.SUCCESS(f'Created Site: {site.domain}'))
