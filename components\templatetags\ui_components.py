from django import template
from django.utils.safestring import mark_safe
import json

register = template.Library()

@register.simple_tag
def button(text, type="primary", size="base", variant="fill", href=None, icon=None, icon_position="left", classes="", attrs=None):
    """
    Renders a button component with consistent styling.
    
    Args:
        text (str): The button text
        type (str): primary, secondary, tertiary, success, danger, warning, info
        size (str): sm, base, lg
        variant (str): fill, outline, text
        href (str, optional): URL for link buttons
        icon (str, optional): FontAwesome icon class (e.g., "fa-plus")
        icon_position (str): left or right
        classes (str, optional): Additional CSS classes
        attrs (dict, optional): Additional HTML attributes
    
    Returns:
        str: HTML for the button
    """
    # Base class
    base_class = "btn"
    
    # Type class
    type_class = f"btn-{type}"
    
    # Size class
    size_class = ""
    if size != "base":
        size_class = f"btn-{size}"
    
    # Variant class
    variant_class = ""
    if variant != "fill":
        variant_class = f"btn-{variant}"
    
    # Combine classes
    css_classes = f"{base_class} {type_class} {size_class} {variant_class} {classes}".strip()
    
    # Prepare attributes
    attributes = ""
    if attrs:
        if isinstance(attrs, str):
            try:
                attrs_dict = json.loads(attrs.replace("'", '"'))
                for key, value in attrs_dict.items():
                    attributes += f' {key}="{value}"'
            except json.JSONDecodeError:
                # If it's not valid JSON, try to parse it as a Python dict literal
                try:
                    attrs_dict = eval(attrs)
                    for key, value in attrs_dict.items():
                        attributes += f' {key}="{value}"'
                except:
                    pass
        elif isinstance(attrs, dict):
            for key, value in attrs.items():
                attributes += f' {key}="{value}"'
    
    # Prepare icon
    icon_html = ""
    if icon:
        icon_html = f'<i class="fas {icon}"></i>'
    
    # Create button content
    if icon and icon_position == "left":
        content = f"{icon_html} {text}"
    elif icon and icon_position == "right":
        content = f"{text} {icon_html}"
    else:
        content = text
    
    # Create button or link
    if href:
        return mark_safe(f'<a href="{href}" class="{css_classes}"{attributes}>{content}</a>')
    else:
        return mark_safe(f'<button class="{css_classes}"{attributes}>{content}</button>')
