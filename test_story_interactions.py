#!/usr/bin/env python
"""
Test script for story interactions (likes and comments)
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'petpaw.settings')
django.setup()

from django.contrib.auth import get_user_model
from social.models import Story, Notification
from messaging.models import Conversation, Message

User = get_user_model()

def test_story_interactions():
    """Test story like and comment functionality"""

    # Get users
    try:
        user1 = User.objects.get(username='admin')
        user2 = User.objects.get(username='akash')
    except User.DoesNotExist:
        print("❌ Required users not found. Please ensure 'admin' and 'akash' users exist.")
        return

    # Get a story
    story = Story.objects.filter(user=user1).first()
    if not story:
        print("❌ No stories found for admin user.")
        return

    print(f"✅ Testing with story {story.id} by {story.user.username}")

    # Test story like notification
    print("\n🧪 Testing story like notification...")

    # Create a story like notification
    like_notification = Notification.objects.create(
        recipient=story.user,
        sender=user2,
        notification_type='story_like',
        story=story,
        message=f"{user2.username} liked your story."
    )
    print(f"✅ Created story like notification: {like_notification.id}")

    # Test story comment notification and message
    print("\n🧪 Testing story comment notification and message...")

    # Create story comment notification
    comment_text = "Great story! Love your pet! 🐕"
    comment_notification = Notification.objects.create(
        recipient=story.user,
        sender=user2,
        notification_type='story_comment',
        story=story,
        story_comment_text=comment_text,
        message=f"{user2.username} commented on your story."
    )
    print(f"✅ Created story comment notification: {comment_notification.id}")

    # Create conversation and message
    # Find existing conversation
    conversation = Conversation.objects.filter(
        participants=user1
    ).filter(
        participants=user2
    ).first()

    if not conversation:
        conversation = Conversation.objects.create()
        conversation.participants.add(user1, user2)
        print(f"✅ Created new conversation: {conversation.id}")
    else:
        print(f"✅ Using existing conversation: {conversation.id}")

    # Create message with story reference
    message = Message.objects.create(
        conversation=conversation,
        sender=user2,
        content=f"Commented on your story: {comment_text}",
        story=story
    )
    print(f"✅ Created story comment message: {message.id}")

    # Summary
    print(f"\n📊 Test Summary:")
    print(f"   Story: {story.id} by {story.user.username}")
    print(f"   Like notification: {like_notification.id}")
    print(f"   Comment notification: {comment_notification.id}")
    print(f"   Conversation: {conversation.id}")
    print(f"   Message: {message.id}")

    print(f"\n🎯 Test URLs to check:")
    print(f"   Story: http://127.0.0.1:8000/social/story/{story.id}/")
    print(f"   Notifications: http://127.0.0.1:8000/social/notifications/")
    print(f"   Messages: http://127.0.0.1:8000/messaging/conversation/{conversation.id}/")

    print(f"\n✅ Story interaction test completed successfully!")

if __name__ == "__main__":
    test_story_interactions()
