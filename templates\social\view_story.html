{% extends 'base.html' %}

{% block title %}{{ story.user.username }}'s Story | PetPaw{% endblock %}

{% block extra_css %}
<style>
    body {
        background: #000;
        margin: 0;
        padding: 0;
        overflow: hidden;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    .story-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: #000;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .story-card {
        position: relative;
        width: 100%;
        height: 100%;
        max-width: 400px;
        background: #000;
        overflow: hidden;
        border-radius: 0;
    }

    .story-progress {
        position: absolute;
        top: 12px;
        left: 12px;
        right: 12px;
        display: flex;
        gap: 4px;
        z-index: 30;
    }

    .story-progress-bar {
        flex: 1;
        height: 2px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 1px;
        overflow: hidden;
    }

    .story-progress-fill {
        height: 100%;
        background: #fff;
        width: 0;
        transition: width 5s linear;
        border-radius: 1px;
    }

    .story-progress-fill.active {
        width: 100%;
    }

    .story-header {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 40px 16px 16px;
        background: linear-gradient(180deg, rgba(0,0,0,0.6) 0%, rgba(0,0,0,0.3) 50%, transparent 100%);
        z-index: 20;
    }

    .story-user {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .story-user-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        object-fit: cover;
        border: 2px solid #fff;
    }

    .story-user-info {
        display: flex;
        flex-direction: column;
    }

    .story-user-name {
        color: #fff;
        font-size: 14px;
        font-weight: 600;
        margin: 0;
        text-shadow: 0 1px 2px rgba(0,0,0,0.5);
    }

    .story-time {
        color: rgba(255, 255, 255, 0.8);
        font-size: 12px;
        margin: 0;
        text-shadow: 0 1px 2px rgba(0,0,0,0.5);
    }

    .story-actions {
        display: flex;
        align-items: center;
        gap: 16px;
    }

    .story-action-btn {
        background: none;
        border: none;
        color: #fff;
        font-size: 20px;
        cursor: pointer;
        padding: 8px;
        border-radius: 50%;
        transition: background-color 0.2s;
        text-shadow: 0 1px 2px rgba(0,0,0,0.5);
    }

    .story-action-btn:hover {
        background: rgba(255, 255, 255, 0.1);
    }

    .story-close {
        background: none;
        border: none;
        color: #fff;
        font-size: 24px;
        cursor: pointer;
        padding: 4px;
        text-shadow: 0 1px 2px rgba(0,0,0,0.5);
    }

    .story-content {
        position: relative;
        width: 100%;
        height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #000;
    }

    .story-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
    }

    .story-video {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
    }

    .story-caption {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 60px 20px 40px;
        background: linear-gradient(0deg, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.4) 50%, transparent 100%);
        color: #fff;
        font-size: 16px;
        line-height: 1.4;
        text-align: left;
        text-shadow: 0 1px 2px rgba(0,0,0,0.5);
    }

    .story-navigation {
        position: absolute;
        top: 0;
        bottom: 0;
        width: 100%;
        display: flex;
        z-index: 10;
    }

    .story-nav-prev, .story-nav-next {
        flex: 1;
        background: none;
        border: none;
        cursor: pointer;
        outline: none;
        position: relative;
    }

    .story-nav-prev::before,
    .story-nav-next::before {
        content: '';
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        width: 12px;
        height: 12px;
        border: 2px solid rgba(255, 255, 255, 0.6);
        border-radius: 2px;
        opacity: 0;
        transition: opacity 0.2s;
    }

    .story-nav-prev::before {
        left: 20px;
        border-right: none;
        border-bottom: none;
        transform: translateY(-50%) rotate(45deg);
    }

    .story-nav-next::before {
        right: 20px;
        border-left: none;
        border-bottom: none;
        transform: translateY(-50%) rotate(-45deg);
    }

    .story-nav-prev:hover::before,
    .story-nav-next:hover::before {
        opacity: 1;
    }

    .story-bottom-actions {
        position: absolute;
        bottom: 20px;
        left: 20px;
        right: 20px;
        display: flex;
        align-items: center;
        gap: 16px;
        z-index: 20;
    }

    .story-reply-input {
        flex: 1;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 24px;
        padding: 12px 16px;
        color: #fff;
        font-size: 14px;
        outline: none;
        backdrop-filter: blur(10px);
    }

    .story-reply-input::placeholder {
        color: rgba(255, 255, 255, 0.6);
    }

    .story-reply-input:focus {
        border-color: rgba(255, 255, 255, 0.4);
        background: rgba(255, 255, 255, 0.15);
    }

    .story-like-btn {
        background: none;
        border: none;
        color: #fff;
        font-size: 24px;
        cursor: pointer;
        padding: 8px;
        transition: transform 0.2s;
    }

    .story-like-btn:hover {
        transform: scale(1.1);
    }

    .story-like-btn.liked {
        color: #ff3040;
    }

    @media (max-width: 768px) {
        .story-card {
            max-width: 100%;
            border-radius: 0;
        }

        .story-header {
            padding: 50px 16px 16px;
        }

        .story-caption {
            padding: 40px 16px 30px;
            font-size: 15px;
        }

        .story-bottom-actions {
            bottom: 30px;
            left: 16px;
            right: 16px;
        }
    }

    @media (min-width: 769px) {
        .story-container {
            padding: 20px;
        }

        .story-card {
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="story-container">
    <div class="story-card">
        <!-- Progress bars at the top -->
        <div class="story-progress">
            <div class="story-progress-bar">
                <div class="story-progress-fill active"></div>
            </div>
        </div>

        <!-- Header with user info and actions -->
        <div class="story-header">
            <div class="story-user">
                <img src="{{ story.user.profile_picture.url }}" alt="{{ story.user.username }}" class="story-user-avatar">
                <div class="story-user-info">
                    <div class="story-user-name">{{ story.user.username }}</div>
                    <div class="story-time">{{ story.created_at|timesince }} ago</div>
                </div>
            </div>

            <div class="story-actions">
                <button class="story-action-btn" title="More options">
                    <i class="fas fa-ellipsis-h"></i>
                </button>
                <a href="{% url 'feed' %}" class="story-close" title="Close">
                    <i class="fas fa-times"></i>
                </a>
            </div>
        </div>

        <!-- Main story content -->
        <div class="story-content">
            {% if story.image %}
                <img src="{{ story.image.url }}" alt="{{ story.caption }}" class="story-image">
            {% elif story.video %}
                <video src="{{ story.video.url }}" autoplay muted loop class="story-video"></video>
            {% endif %}
        </div>

        <!-- Caption overlay -->
        {% if story.caption %}
            <div class="story-caption">
                {{ story.caption }}
            </div>
        {% endif %}

        <!-- Bottom interaction area -->
        <div class="story-bottom-actions">
            <input type="text" placeholder="Reply to {{ story.user.username }}..." class="story-reply-input">
            <button class="story-like-btn" title="Like">
                <i class="far fa-heart"></i>
            </button>
        </div>

        <!-- Navigation areas -->
        <div class="story-navigation">
            {% if prev_story_id %}
                <a href="{% url 'view-story' prev_story_id %}" class="story-nav-prev" title="Previous story"></a>
            {% else %}
                <div class="story-nav-prev"></div>
            {% endif %}

            {% if next_story_id %}
                <a href="{% url 'view-story' next_story_id %}" class="story-nav-next" title="Next story"></a>
            {% else %}
                <div class="story-nav-next"></div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        let progressTimer;
        let isPaused = false;

        // Progress bar animation
        const progressFill = document.querySelector('.story-progress-fill');
        const storyDuration = 5000; // 5 seconds

        function startProgress() {
            if (progressFill && !isPaused) {
                progressFill.style.transition = `width ${storyDuration}ms linear`;
                progressFill.style.width = '100%';
            }
        }

        function pauseProgress() {
            if (progressFill) {
                const currentWidth = progressFill.getBoundingClientRect().width;
                const containerWidth = progressFill.parentElement.getBoundingClientRect().width;
                const percentage = (currentWidth / containerWidth) * 100;
                progressFill.style.transition = 'none';
                progressFill.style.width = percentage + '%';
                isPaused = true;
            }
        }

        function resumeProgress() {
            if (progressFill && isPaused) {
                const currentWidth = parseFloat(progressFill.style.width);
                const remainingTime = ((100 - currentWidth) / 100) * storyDuration;
                progressFill.style.transition = `width ${remainingTime}ms linear`;
                progressFill.style.width = '100%';
                isPaused = false;
            }
        }

        // Start progress animation
        startProgress();

        // Auto-navigate to next story
        const nextStoryId = '{{ next_story_id|default:"" }}';

        progressTimer = setTimeout(function() {
            if (nextStoryId && nextStoryId !== 'None' && nextStoryId !== '') {
                window.location.href = "/social/story/" + nextStoryId + "/";
            } else {
                window.location.href = "{% url 'feed' %}";
            }
        }, storyDuration);

        // Pause/resume on click
        const storyContent = document.querySelector('.story-content');
        if (storyContent) {
            storyContent.addEventListener('click', function(e) {
                // Don't pause if clicking on navigation areas
                if (e.target.closest('.story-nav-prev') || e.target.closest('.story-nav-next')) {
                    return;
                }

                if (isPaused) {
                    resumeProgress();
                    progressTimer = setTimeout(function() {
                        if (nextStoryId && nextStoryId !== 'None' && nextStoryId !== '') {
                            window.location.href = "/social/story/" + nextStoryId + "/";
                        } else {
                            window.location.href = "{% url 'feed' %}";
                        }
                    }, ((100 - parseFloat(progressFill.style.width)) / 100) * storyDuration);
                } else {
                    pauseProgress();
                    clearTimeout(progressTimer);
                }
            });
        }

        // Like button functionality
        const likeBtn = document.querySelector('.story-like-btn');
        if (likeBtn) {
            likeBtn.addEventListener('click', function() {
                const icon = this.querySelector('i');
                if (this.classList.contains('liked')) {
                    this.classList.remove('liked');
                    icon.className = 'far fa-heart';
                } else {
                    this.classList.add('liked');
                    icon.className = 'fas fa-heart';
                    // Add animation
                    this.style.transform = 'scale(1.3)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 200);
                }
            });
        }

        // Reply input functionality
        const replyInput = document.querySelector('.story-reply-input');
        if (replyInput) {
            replyInput.addEventListener('focus', function() {
                pauseProgress();
                clearTimeout(progressTimer);
            });

            replyInput.addEventListener('blur', function() {
                if (!this.value.trim()) {
                    resumeProgress();
                    progressTimer = setTimeout(function() {
                        if (nextStoryId && nextStoryId !== 'None' && nextStoryId !== '') {
                            window.location.href = "/social/story/" + nextStoryId + "/";
                        } else {
                            window.location.href = "{% url 'feed' %}";
                        }
                    }, ((100 - parseFloat(progressFill.style.width)) / 100) * storyDuration);
                }
            });

            replyInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && this.value.trim()) {
                    // Handle reply submission here
                    console.log('Reply:', this.value);
                    this.value = '';
                    this.blur();
                }
            });
        }

        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            const prevStoryId = '{{ prev_story_id|default:"" }}';
            const nextStoryId = '{{ next_story_id|default:"" }}';

            if (e.key === 'ArrowLeft' && prevStoryId && prevStoryId !== 'None' && prevStoryId !== '') {
                window.location.href = "/social/story/" + prevStoryId + "/";
            } else if (e.key === 'ArrowRight' && nextStoryId && nextStoryId !== 'None' && nextStoryId !== '') {
                window.location.href = "/social/story/" + nextStoryId + "/";
            } else if (e.key === 'Escape') {
                window.location.href = "{% url 'feed' %}";
            } else if (e.key === ' ') { // Spacebar to pause/resume
                e.preventDefault();
                if (isPaused) {
                    resumeProgress();
                } else {
                    pauseProgress();
                    clearTimeout(progressTimer);
                }
            }
        });

        // Touch gestures for mobile
        let touchStartX = 0;
        let touchStartY = 0;

        document.addEventListener('touchstart', function(e) {
            touchStartX = e.touches[0].clientX;
            touchStartY = e.touches[0].clientY;
        });

        document.addEventListener('touchend', function(e) {
            const touchEndX = e.changedTouches[0].clientX;
            const touchEndY = e.changedTouches[0].clientY;
            const deltaX = touchEndX - touchStartX;
            const deltaY = touchEndY - touchStartY;

            // Only handle horizontal swipes
            if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
                const prevStoryId = '{{ prev_story_id|default:"" }}';
                const nextStoryId = '{{ next_story_id|default:"" }}';

                if (deltaX > 0 && prevStoryId && prevStoryId !== 'None' && prevStoryId !== '') {
                    // Swipe right - previous story
                    window.location.href = "/social/story/" + prevStoryId + "/";
                } else if (deltaX < 0 && nextStoryId && nextStoryId !== 'None' && nextStoryId !== '') {
                    // Swipe left - next story
                    window.location.href = "/social/story/" + nextStoryId + "/";
                }
            }
        });

        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            clearTimeout(progressTimer);
        });
    });
</script>
{% endblock %}
