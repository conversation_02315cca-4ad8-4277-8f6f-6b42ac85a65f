import json
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from .models import Conversation, Message
from users.models import User
from social.models import Notification


class ChatConsumer(AsyncWebsocketConsumer):
    """WebSocket consumer for real-time chat"""

    async def connect(self):
        """Handle WebSocket connection"""
        self.user = self.scope['user']
        self.conversation_id = self.scope['url_route']['kwargs']['conversation_id']
        self.conversation_group_name = f'chat_{self.conversation_id}'

        # Check if user is part of the conversation
        if not await self.is_user_in_conversation():
            await self.close()
            return

        # Join conversation group
        await self.channel_layer.group_add(
            self.conversation_group_name,
            self.channel_name
        )

        await self.accept()

    async def disconnect(self, close_code):
        """Handle WebSocket disconnection"""
        # Leave conversation group
        await self.channel_layer.group_discard(
            self.conversation_group_name,
            self.channel_name
        )

    async def receive(self, text_data):
        """Handle receiving messages from WebSocket"""
        data = json.loads(text_data)
        message_type = data.get('type', 'message')

        if message_type == 'message':
            message = data['message']

            # Save message to database
            message_obj = await self.save_message(message)

            # Send message to conversation group
            await self.channel_layer.group_send(
                self.conversation_group_name,
                {
                    'type': 'chat_message',
                    'message': message,
                    'sender_id': self.user.id,
                    'sender_username': self.user.username,
                    'message_id': message_obj.id,
                    'timestamp': message_obj.created_at.isoformat()
                }
            )
        elif message_type == 'typing':
            # Send typing status to conversation group
            await self.channel_layer.group_send(
                self.conversation_group_name,
                {
                    'type': 'user_typing',
                    'sender_id': self.user.id,
                    'sender_username': self.user.username,
                }
            )

    async def chat_message(self, event):
        """Send message to WebSocket"""
        # Send message to WebSocket
        await self.send(text_data=json.dumps({
            'type': 'message',
            'message': event['message'],
            'sender_id': event['sender_id'],
            'sender_username': event['sender_username'],
            'message_id': event['message_id'],
            'timestamp': event['timestamp'],
            'is_self': event['sender_id'] == self.user.id
        }))

    async def user_typing(self, event):
        """Send typing status to WebSocket"""
        # Don't send typing status back to the sender
        if event['sender_id'] != self.user.id:
            await self.send(text_data=json.dumps({
                'type': 'typing',
                'sender_id': event['sender_id'],
                'sender_username': event['sender_username']
            }))

    @database_sync_to_async
    def is_user_in_conversation(self):
        """Check if user is part of the conversation"""
        try:
            conversation = Conversation.objects.get(id=self.conversation_id)
            return conversation.participants.filter(id=self.user.id).exists()
        except Conversation.DoesNotExist:
            return False

    @database_sync_to_async
    def save_message(self, content):
        """Save message to database"""
        conversation = Conversation.objects.get(id=self.conversation_id)
        message = Message.objects.create(
            conversation=conversation,
            sender=self.user,
            content=content
        )
        # Update conversation timestamp
        conversation.save()

        # Create notification for the other participant
        other_user = conversation.get_other_participant(self.user)
        if other_user:
            Notification.objects.create(
                recipient=other_user,
                sender=self.user,
                notification_type='message',
                conversation=conversation,
                message=f"{self.user.username} sent you a message."
            )

        return message
