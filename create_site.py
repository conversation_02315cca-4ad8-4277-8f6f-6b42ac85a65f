import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'petpaw.settings')
django.setup()

# Import Site model
from django.contrib.sites.models import Site

# Create or update the Site object
try:
    site = Site.objects.get(id=1)
    print(f'Site already exists: {site.domain}')
    site.domain = 'example.com'
    site.name = 'PetPaw'
    site.save()
    print(f'Updated Site: {site.domain}')
except Site.DoesNotExist:
    site = Site.objects.create(id=1, domain='example.com', name='PetPaw')
    print(f'Created Site: {site.domain}')
