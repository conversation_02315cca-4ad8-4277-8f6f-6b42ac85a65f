/* Footer Styles */
.main-footer {
    background-color: var(--gray-900);
    color: var(--white);
    padding: var(--spacing-5xl) 0 var(--spacing-3xl);
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: var(--gap-3xl);
    margin-bottom: var(--spacing-3xl);
}

.footer-logo {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.footer-logo .logo-container {
    font-size: var(--font-h3);
    font-weight: var(--fw-bold);
    color: var(--primary);
}

.footer-logo p {
    color: var(--gray-400);
    margin-top: var(--spacing-md);
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--gap-2xl);
}

.footer-section h4 {
    color: var(--white);
    margin-bottom: var(--spacing-lg);
    font-size: var(--font-tl);
}

.footer-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: var(--gap-md);
}

.footer-section ul li a {
    color: var(--gray-400);
    transition: var(--transition-base);
    text-decoration: none;
}

.footer-section ul li a:hover {
    color: var(--primary);
    text-decoration: none;
}

.social-links {
    display: flex;
    gap: var(--gap-lg);
    margin-bottom: var(--spacing-lg);
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: var(--radius-circle);
    background-color: var(--gray-800);
    color: var(--white);
    transition: var(--transition-base);
}

.social-links a:hover {
    background-color: var(--primary);
    color: var(--white);
    text-decoration: none;
}

.newsletter h5 {
    color: var(--white);
    margin-bottom: var(--spacing-md);
    font-size: var(--font-lg);
}

.newsletter form {
    display: flex;
    gap: var(--gap-md);
}

.newsletter input {
    flex: 1;
    padding: var(--spacing-md) var(--spacing-r);
    border-radius: var(--radius-lg);
    border: 1px solid var(--gray-700);
    background-color: var(--gray-800);
    color: var(--white);
}

.newsletter button {
    padding: var(--spacing-md) var(--spacing-r);
    border-radius: var(--radius-lg);
    border: none;
    background-color: var(--primary);
    color: var(--white);
    cursor: pointer;
    transition: var(--transition-base);
}

.newsletter button:hover {
    background-color: var(--button-fill-hover);
}

.footer-bottom {
    border-top: 1px solid var(--gray-800);
    padding-top: var(--spacing-xl);
    text-align: center;
}

.footer-bottom p {
    color: var(--gray-500);
    font-size: var(--font-sm);
}

/* Responsive styles */
@media (max-width: 992px) {
    .footer-content {
        grid-template-columns: 1fr;
    }
    
    .footer-links {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .footer-links {
        grid-template-columns: 1fr;
        gap: var(--gap-xl);
    }
    
    .footer-section {
        margin-bottom: var(--spacing-xl);
    }
}
