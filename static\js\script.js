// Initialize Lenis with autoRaf enabled
let lenis;

// Initialize Lenis only if it exists
if (typeof Lenis !== 'undefined') {
  lenis = new Lenis({
    autoRaf: true
  });

  // Use requestAnimationFrame to continuously update the scroll
  function raf(time) {
    lenis.raf(time);
    requestAnimationFrame(raf);
  }

  requestAnimationFrame(raf);
}

// Wait for DOM to be fully loaded
$(document).ready(function() {
  // Initialize Slick Carousel
  $('.home-list-item-container').slick({
    centerMode: true,
    centerPadding: '60px',
    slidesToShow: 3,
    variableWidth: true,
    infinite: true,
    autoplay: true,
    autoplaySpeed: 3000,
    speed: 600,
    cssEase: 'cubic-bezier(0.25, 1, 0.5, 1)',
    arrows: true, // Enable default arrows
    appendArrows: $('.action-container .icons-container'), // Append arrows to your custom container
    prevArrow: '<button class="icon previous"><img src="./img/arrow_previous.svg" alt="Previous"></button>',
    nextArrow: '<button class="icon next"><img src="./img/arrow_next.svg" alt="Next"></button>',
    // Add 24px gap between slides
    swipeToSlide: true,
    // Add pause on hover functionality
    onInit: function() {
      // Pause autoplay on hover over the navigation buttons
      $('.action-container .icon').hover(
        function() { $('.home-list-item-container').slick('slickPause'); },
        function() { $('.home-list-item-container').slick('slickPlay'); }
      );

      // Add click effect
      $('.action-container .icon').on('click', function() {
        const $this = $(this);
        $this.addClass('clicked');
        setTimeout(function() { $this.removeClass('clicked'); }, 200);
      });
    },
    responsive: [
      {
        breakpoint: 992,
        settings: {
          centerMode: true,
          centerPadding: '40px',
          slidesToShow: 2
        }
      },
      {
        breakpoint: 768,
        settings: {
          centerMode: true,
          centerPadding: '30px',
          slidesToShow: 1
        }
      }
    ]
  });

  // GSAP animation for enlarging the dog image on hover
  $('.home-list-item').hover(
    function() {
      // Mouse enter
      const img = $(this).find('img');
      gsap.to(img[0], {
        scale: 1.2,
        y: '-80px',
        duration: 0.5,
        ease: 'power2.out',
      });
    },
    function() {
      // Mouse leave
      const img = $(this).find('img');
      gsap.to(img[0], {
        scale: 1,
        y: '0px',
        duration: 0.5,
        ease: 'power2.out',
      });
    }
  );
});
