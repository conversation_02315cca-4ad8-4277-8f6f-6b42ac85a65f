{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}Manage Availability - PetPaw{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3">
            <div class="card shadow-sm mb-4">
                <div class="card-body text-center">
                    {% if user.service_provider.profile_picture %}
                        <img src="{{ user.service_provider.profile_picture.url }}" alt="{{ user.username }}" class="rounded-circle img-fluid mb-3" style="width: 120px; height: 120px; object-fit: cover;">
                    {% else %}
                        <img src="/static/img/default-profile.png" alt="{{ user.username }}" class="rounded-circle img-fluid mb-3" style="width: 120px; height: 120px; object-fit: cover;">
                    {% endif %}
                    <h5 class="mb-0">{{ user.get_full_name|default:user.username }}</h5>
                    <p class="text-muted">Service Provider</p>
                    <div class="d-grid gap-2">
                        <a href="{% url 'provider-detail' user.service_provider.id %}" class="btn btn-outline-primary btn-sm">View Public Profile</a>
                    </div>
                </div>
                <div class="list-group list-group-flush">
                    <a href="{% url 'provider-dashboard' %}" class="list-group-item list-group-item-action {% if active_tab == 'dashboard' %}active{% endif %}">
                        <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                    </a>
                    <a href="{% url 'provider-services' %}" class="list-group-item list-group-item-action {% if active_tab == 'services' %}active{% endif %}">
                        <i class="fas fa-concierge-bell me-2"></i> My Services
                    </a>
                    <a href="{% url 'provider-bookings' %}" class="list-group-item list-group-item-action {% if active_tab == 'bookings' %}active{% endif %}">
                        <i class="fas fa-calendar-check me-2"></i> Bookings
                    </a>
                    <a href="{% url 'provider-availability' %}" class="list-group-item list-group-item-action {% if active_tab == 'availability' %}active{% endif %}">
                        <i class="fas fa-clock me-2"></i> Availability
                    </a>
                    <a href="{% url 'provider-settings' %}" class="list-group-item list-group-item-action {% if active_tab == 'settings' %}active{% endif %}">
                        <i class="fas fa-cog me-2"></i> Settings
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="col-md-9">
            <!-- Availability Header -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Manage Availability</h5>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addAvailabilityModal">
                        <i class="fas fa-plus-circle me-2"></i> Add Availability
                    </button>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Day</th>
                                            <th>Morning (6AM-12PM)</th>
                                            <th>Afternoon (12PM-5PM)</th>
                                            <th>Evening (5PM-10PM)</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for day_name, day_slots in availability_by_day.items %}
                                        <tr>
                                            <td class="fw-bold">{{ day_name }}</td>
                                            <td>
                                                {% for slot in day_slots.morning %}
                                                <div class="d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded">
                                                    <span>{{ slot.start_time|time:"g:i A" }} - {{ slot.end_time|time:"g:i A" }}</span>
                                                    <form action="{% url 'delete-availability' slot.id %}" method="post" class="d-inline">
                                                        {% csrf_token %}
                                                        <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('Are you sure you want to delete this availability slot?')">
                                                            <i class="fas fa-trash-alt"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                                {% empty %}
                                                <p class="text-muted small mb-0">No availability</p>
                                                {% endfor %}
                                            </td>
                                            <td>
                                                {% for slot in day_slots.afternoon %}
                                                <div class="d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded">
                                                    <span>{{ slot.start_time|time:"g:i A" }} - {{ slot.end_time|time:"g:i A" }}</span>
                                                    <form action="{% url 'delete-availability' slot.id %}" method="post" class="d-inline">
                                                        {% csrf_token %}
                                                        <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('Are you sure you want to delete this availability slot?')">
                                                            <i class="fas fa-trash-alt"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                                {% empty %}
                                                <p class="text-muted small mb-0">No availability</p>
                                                {% endfor %}
                                            </td>
                                            <td>
                                                {% for slot in day_slots.evening %}
                                                <div class="d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded">
                                                    <span>{{ slot.start_time|time:"g:i A" }} - {{ slot.end_time|time:"g:i A" }}</span>
                                                    <form action="{% url 'delete-availability' slot.id %}" method="post" class="d-inline">
                                                        {% csrf_token %}
                                                        <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('Are you sure you want to delete this availability slot?')">
                                                            <i class="fas fa-trash-alt"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                                {% empty %}
                                                <p class="text-muted small mb-0">No availability</p>
                                                {% endfor %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Availability Tips -->
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Availability Tips</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12">
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item">
                                    <i class="fas fa-info-circle text-primary me-2"></i>
                                    Set your availability for each day of the week to let clients know when you can provide services.
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-info-circle text-primary me-2"></i>
                                    You can add multiple time slots for each day to accommodate different schedules.
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-info-circle text-primary me-2"></i>
                                    Clients can only book appointments during your available time slots.
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-info-circle text-primary me-2"></i>
                                    Remember to keep your availability up to date to avoid scheduling conflicts.
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Availability Modal -->
<div class="modal fade" id="addAvailabilityModal" tabindex="-1" aria-labelledby="addAvailabilityModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addAvailabilityModalLabel">Add Availability</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'add-availability' %}">
                <div class="modal-body">
                    {% csrf_token %}
                    {{ availability_form|crispy }}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Availability</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
