# Generated by Django 4.2.7 on 2025-05-17 21:52

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Pet',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('birth_date', models.DateField(blank=True, null=True)),
                ('gender', models.CharField(choices=[('M', 'Male'), ('F', 'Female'), ('U', 'Unknown')], default='U', max_length=1)),
                ('profile_picture', models.ImageField(default='default_pet.jpg', upload_to='pet_profiles')),
                ('bio', models.TextField(blank=True)),
                ('is_for_adoption', models.BooleanField(default=False)),
                ('adoption_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='PetCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('icon', models.ImageField(blank=True, upload_to='category_icons')),
            ],
            options={
                'verbose_name_plural': 'Pet Categories',
            },
        ),
        migrations.CreateModel(
            name='PetMedicalRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('record_date', models.DateField()),
                ('record_type', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('veterinarian', models.CharField(blank=True, max_length=255)),
                ('clinic', models.CharField(blank=True, max_length=255)),
                ('document', models.FileField(blank=True, upload_to='pet_medical_records')),
                ('pet', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='medical_records', to='pets.pet')),
            ],
        ),
        migrations.CreateModel(
            name='PetGallery',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to='pet_gallery')),
                ('caption', models.CharField(blank=True, max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('pet', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='gallery', to='pets.pet')),
            ],
            options={
                'verbose_name_plural': 'Pet Galleries',
            },
        ),
        migrations.CreateModel(
            name='PetBreed',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('average_lifespan', models.CharField(blank=True, max_length=50)),
                ('size', models.CharField(blank=True, max_length=50)),
                ('temperament', models.CharField(blank=True, max_length=255)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='breeds', to='pets.petcategory')),
            ],
        ),
        migrations.AddField(
            model_name='pet',
            name='breed',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='pets', to='pets.petbreed'),
        ),
        migrations.AddField(
            model_name='pet',
            name='category',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='pets', to='pets.petcategory'),
        ),
    ]
