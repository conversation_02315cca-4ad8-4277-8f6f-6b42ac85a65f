{% extends 'base.html' %}
{% load static %}

{% block title %}Booking Details - PetPaw{% endblock %}

{% block content %}
<div class="container">
    <div class="booking-detail-header">
        <a href="{% url 'booking-list' %}" class="back-link">
            <i class="fas fa-arrow-left"></i>
            Back to Bookings
        </a>
        <h1>Booking Details</h1>
    </div>

    <div class="booking-detail-card">
        <div class="booking-status-header">
            <div class="service-info">
                <h2>{{ booking.service.name }}</h2>
                <p class="provider-name">
                    <i class="fas fa-user"></i>
                    {{ booking.service.provider.user.get_full_name|default:booking.service.provider.user.username }}
                </p>
            </div>
            <div class="status-section">
                <span class="status-badge status-{{ booking.status }}">
                    {{ booking.get_status_display }}
                </span>
            </div>
        </div>

        <div class="booking-details-grid">
            <div class="detail-section">
                <h3>Booking Information</h3>
                <div class="detail-list">
                    <div class="detail-item">
                        <span class="label">Date:</span>
                        <span class="value">{{ booking.date|date:"F d, Y" }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="label">Time:</span>
                        <span class="value">{{ booking.start_time|time:"g:i A" }} - {{ booking.end_time|time:"g:i A" }}</span>
                    </div>
                    <div class="detail-item">
                        <span class="label">Duration:</span>
                        <span class="value">{{ booking.duration }} hours</span>
                    </div>
                    <div class="detail-item">
                        <span class="label">Total Price:</span>
                        <span class="value price">${{ booking.total_price }}</span>
                    </div>
                    {% if booking.pet %}
                        <div class="detail-item">
                            <span class="label">Pet:</span>
                            <span class="value">{{ booking.pet.name }}</span>
                        </div>
                    {% endif %}
                </div>
            </div>

            <div class="detail-section">
                <h3>Service Details</h3>
                <div class="service-card">
                    <div class="service-description">
                        <p>{{ booking.service.description }}</p>
                    </div>
                    <div class="service-price">
                        <span class="price-label">Hourly Rate:</span>
                        <span class="price-value">${{ booking.service.price_per_hour }}/hour</span>
                    </div>
                </div>
            </div>
        </div>

        {% if booking.notes %}
            <div class="notes-section">
                <h3>Special Notes</h3>
                <div class="notes-content">
                    <p>{{ booking.notes }}</p>
                </div>
            </div>
        {% endif %}

        <div class="actions-section">
            {% if booking.user == request.user %}
                <!-- Customer actions -->
                {% if booking.status == 'pending' %}
                    <button class="btn btn-danger" onclick="cancelBooking({{ booking.pk }})">
                        <i class="fas fa-times"></i>
                        Cancel Booking
                    </button>
                {% endif %}
                
                {% if can_review %}
                    <button class="btn btn-primary" onclick="showReviewForm()">
                        <i class="fas fa-star"></i>
                        Write Review
                    </button>
                {% endif %}
            {% elif booking.service.provider.user == request.user %}
                <!-- Provider actions -->
                {% if booking.status == 'pending' %}
                    <form method="post" action="{% url 'update-booking-status' booking.pk %}" style="display: inline;">
                        {% csrf_token %}
                        <input type="hidden" name="status" value="confirmed">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-check"></i>
                            Confirm Booking
                        </button>
                    </form>
                    <form method="post" action="{% url 'update-booking-status' booking.pk %}" style="display: inline;">
                        {% csrf_token %}
                        <input type="hidden" name="status" value="cancelled">
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-times"></i>
                            Decline Booking
                        </button>
                    </form>
                {% elif booking.status == 'confirmed' %}
                    <form method="post" action="{% url 'update-booking-status' booking.pk %}" style="display: inline;">
                        {% csrf_token %}
                        <input type="hidden" name="status" value="completed">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-check-circle"></i>
                            Mark as Completed
                        </button>
                    </form>
                {% endif %}
            {% endif %}
        </div>
    </div>

    {% if can_review %}
        <!-- Review Form Modal -->
        <div id="reviewModal" class="modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Write a Review</h3>
                    <button class="modal-close" onclick="hideReviewForm()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <form method="post" action="{% url 'add-service-review' booking.pk %}">
                    {% csrf_token %}
                    <div class="modal-body">
                        {{ review_form.as_p }}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="hideReviewForm()">Cancel</button>
                        <button type="submit" class="btn btn-primary">Submit Review</button>
                    </div>
                </form>
            </div>
        </div>
    {% endif %}
</div>

<style>
.booking-detail-header {
    margin-bottom: var(--spacing-xl);
}

.back-link {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--primary);
    text-decoration: none;
    margin-bottom: var(--spacing-lg);
    font-weight: 500;
    transition: var(--transition-base);
}

.back-link:hover {
    color: var(--primary-dark);
}

.booking-detail-header h1 {
    color: var(--gray-800);
    margin: 0;
}

.booking-detail-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    overflow: hidden;
}

.booking-status-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: var(--spacing-xl);
    background: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
}

.service-info h2 {
    color: var(--primary);
    margin-bottom: var(--spacing-xs);
}

.provider-name {
    color: var(--gray-600);
    margin: 0;
}

.provider-name i {
    margin-right: var(--spacing-xs);
}

.status-badge {
    padding: var(--spacing-sm) var(--spacing-base);
    border-radius: var(--radius-full);
    font-weight: 600;
    text-transform: uppercase;
    font-size: var(--font-sm);
}

.booking-details-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    padding: var(--spacing-xl);
}

.detail-section h3 {
    color: var(--gray-800);
    margin-bottom: var(--spacing-base);
    font-size: var(--font-lg);
}

.detail-list {
    display: grid;
    gap: var(--spacing-sm);
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--gray-100);
}

.detail-item:last-child {
    border-bottom: none;
}

.label {
    color: var(--gray-600);
    font-weight: 500;
}

.value {
    color: var(--gray-800);
    font-weight: 600;
}

.value.price {
    color: var(--primary);
    font-size: var(--font-lg);
}

.service-card {
    background: var(--gray-50);
    padding: var(--spacing-base);
    border-radius: var(--radius-md);
    border: 1px solid var(--gray-200);
}

.service-description {
    margin-bottom: var(--spacing-base);
}

.service-price {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.price-label {
    color: var(--gray-600);
}

.price-value {
    color: var(--primary);
    font-weight: 600;
}

.notes-section {
    padding: var(--spacing-xl);
    border-top: 1px solid var(--gray-200);
}

.notes-section h3 {
    color: var(--gray-800);
    margin-bottom: var(--spacing-base);
}

.notes-content {
    background: var(--gray-50);
    padding: var(--spacing-base);
    border-radius: var(--radius-md);
    border-left: 4px solid var(--primary);
}

.actions-section {
    padding: var(--spacing-xl);
    background: var(--gray-50);
    border-top: 1px solid var(--gray-200);
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: var(--z-50);
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: var(--white);
    border-radius: var(--radius-lg);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--gray-200);
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--font-lg);
    color: var(--gray-500);
    cursor: pointer;
}

.modal-body {
    padding: var(--spacing-lg);
}

.modal-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--gray-200);
    display: flex;
    gap: var(--spacing-sm);
    justify-content: flex-end;
}

@media (max-width: 768px) {
    .booking-status-header {
        flex-direction: column;
        gap: var(--spacing-base);
    }
    
    .booking-details-grid {
        grid-template-columns: 1fr;
    }
    
    .actions-section {
        flex-direction: column;
    }
    
    .actions-section .btn {
        width: 100%;
        justify-content: center;
    }
}
</style>

<script>
function showReviewForm() {
    document.getElementById('reviewModal').style.display = 'flex';
}

function hideReviewForm() {
    document.getElementById('reviewModal').style.display = 'none';
}

function cancelBooking(bookingId) {
    if (confirm('Are you sure you want to cancel this booking?')) {
        fetch(`/services/bookings/${bookingId}/cancel/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCookie('csrftoken'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Failed to cancel booking. Please try again.');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to cancel booking. Please try again.');
        });
    }
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
</script>
{% endblock %}
