from django import forms
from .models import Pet, PetGallery, PetMedicalRecord


class PetForm(forms.ModelForm):
    """Form for creating and updating pets"""
    class Meta:
        model = Pet
        fields = ('name', 'category', 'breed', 'birth_date', 'gender', 
                  'profile_picture', 'bio', 'is_for_adoption', 'adoption_price')
        widgets = {
            'birth_date': forms.DateInput(attrs={'type': 'date'}),
            'bio': forms.Textarea(attrs={'rows': 4}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Make breed field dependent on selected category
        if 'category' in self.data:
            try:
                category_id = int(self.data.get('category'))
                self.fields['breed'].queryset = self.fields['breed'].queryset.filter(category_id=category_id)
            except (ValueError, TypeError):
                pass
        elif self.instance.pk and self.instance.category:
            self.fields['breed'].queryset = self.fields['breed'].queryset.filter(category=self.instance.category)


class PetGalleryForm(forms.ModelForm):
    """Form for adding photos to pet gallery"""
    class Meta:
        model = PetGallery
        fields = ('image', 'caption')


class PetMedicalRecordForm(forms.ModelForm):
    """Form for adding medical records to pets"""
    class Meta:
        model = PetMedicalRecord
        fields = ('record_date', 'record_type', 'description', 'veterinarian', 'clinic', 'document')
        widgets = {
            'record_date': forms.DateInput(attrs={'type': 'date'}),
            'description': forms.Textarea(attrs={'rows': 4}),
        }
