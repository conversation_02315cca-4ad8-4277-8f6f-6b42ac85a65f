{% extends 'base.html' %}
{% load static %}

<!-- Temporarily comment out the button component template tag -->
{% comment %}
{% load ui_components %}
{% endcomment %}

{% block title %}Button Examples | PetPaw{% endblock %}

{% block extra_css %}
<style>
    .example-section {
        margin-bottom: var(--spacing-3xl);
    }

    .example-title {
        margin-bottom: var(--spacing-base);
        padding-bottom: var(--spacing-sm);
        border-bottom: 1px solid var(--gray-200);
    }

    .example-description {
        margin-bottom: var(--spacing-lg);
        color: var(--text-light);
    }

    .example-container {
        display: flex;
        flex-wrap: wrap;
        gap: var(--spacing-lg);
        margin-bottom: var(--spacing-xl);
        padding: var(--spacing-xl);
        background-color: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-sm);
    }

    .code-block {
        background-color: var(--gray-100);
        padding: var(--spacing-base);
        border-radius: var(--radius-md);
        font-family: var(--font-family-mono);
        font-size: var(--font-sm);
        overflow-x: auto;
        margin-bottom: var(--spacing-xl);
    }

    .code-title {
        font-weight: var(--fw-semibold);
        margin-bottom: var(--spacing-sm);
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-5">
    <h1 class="mb-4">Button Components</h1>
    <p class="mb-5">This page demonstrates the various button styles available in the PetPaw application.</p>

    <!-- Button Types -->
    <section class="example-section">
        <h2 class="example-title">Button Types</h2>
        <p class="example-description">Different button types for various contexts and actions.</p>

        <div class="example-container">
            <button class="btn btn-primary">Primary</button>
            <button class="btn btn-secondary">Secondary</button>
            <button class="btn btn-tertiary">Tertiary</button>
            <button class="btn btn-success">Success</button>
            <button class="btn btn-danger">Danger</button>
            <button class="btn btn-warning">Warning</button>
            <button class="btn btn-info">Info</button>
        </div>

        <div class="code-block">
            <div class="code-title">HTML Code:</div>
            <pre>&lt;button class="btn btn-primary"&gt;Primary&lt;/button&gt;
&lt;button class="btn btn-secondary"&gt;Secondary&lt;/button&gt;
&lt;button class="btn btn-tertiary"&gt;Tertiary&lt;/button&gt;
&lt;button class="btn btn-success"&gt;Success&lt;/button&gt;
&lt;button class="btn btn-danger"&gt;Danger&lt;/button&gt;
&lt;button class="btn btn-warning"&gt;Warning&lt;/button&gt;
&lt;button class="btn btn-info"&gt;Info&lt;/button&gt;</pre>
        </div>
    </section>

    <!-- Button Sizes -->
    <section class="example-section">
        <h2 class="example-title">Button Sizes</h2>
        <p class="example-description">Buttons come in different sizes to fit various UI contexts.</p>

        <div class="example-container">
            <button class="btn btn-primary btn-sm">Small Button</button>
            <button class="btn btn-primary">Default Button</button>
            <button class="btn btn-primary btn-lg">Large Button</button>
        </div>

        <div class="code-block">
            <div class="code-title">HTML Code:</div>
            <pre>&lt;button class="btn btn-primary btn-sm"&gt;Small Button&lt;/button&gt;
&lt;button class="btn btn-primary"&gt;Default Button&lt;/button&gt;
&lt;button class="btn btn-primary btn-lg"&gt;Large Button&lt;/button&gt;</pre>
        </div>
    </section>

    <!-- Button Variants -->
    <section class="example-section">
        <h2 class="example-title">Button Variants</h2>
        <p class="example-description">Different visual styles for buttons with the same semantic meaning.</p>

        <div class="example-container">
            <button class="btn btn-primary">Fill Button</button>
            <button class="btn btn-primary btn-outline">Outline Button</button>
            <button class="btn btn-primary btn-text">Text Button</button>
        </div>

        <div class="code-block">
            <div class="code-title">HTML Code:</div>
            <pre>&lt;button class="btn btn-primary"&gt;Fill Button&lt;/button&gt;
&lt;button class="btn btn-primary btn-outline"&gt;Outline Button&lt;/button&gt;
&lt;button class="btn btn-primary btn-text"&gt;Text Button&lt;/button&gt;</pre>
        </div>
    </section>

    <!-- Buttons with Icons -->
    <section class="example-section">
        <h2 class="example-title">Buttons with Icons</h2>
        <p class="example-description">Buttons can include icons to enhance visual communication.</p>

        <div class="example-container">
            <button class="btn btn-primary"><i class="fas fa-plus"></i> Add Pet</button>
            <button class="btn btn-secondary"><i class="fas fa-cog"></i> Settings</button>
            <button class="btn btn-success">Download <i class="fas fa-download"></i></button>
        </div>

        <div class="code-block">
            <div class="code-title">HTML Code:</div>
            <pre>&lt;button class="btn btn-primary"&gt;&lt;i class="fas fa-plus"&gt;&lt;/i&gt; Add Pet&lt;/button&gt;
&lt;button class="btn btn-secondary"&gt;&lt;i class="fas fa-cog"&gt;&lt;/i&gt; Settings&lt;/button&gt;
&lt;button class="btn btn-success"&gt;Download &lt;i class="fas fa-download"&gt;&lt;/i&gt;&lt;/button&gt;</pre>
        </div>
    </section>

    <!-- Link Buttons -->
    <section class="example-section">
        <h2 class="example-title">Link Buttons</h2>
        <p class="example-description">Buttons that function as links to other pages.</p>

        <div class="example-container">
            <a href="/" class="btn btn-primary">Go to Home</a>
            <a href="/pets/" class="btn btn-secondary"><i class="fas fa-paw"></i> View Pets</a>
            <a href="/shop/" class="btn btn-tertiary btn-outline">Shop Now</a>
        </div>

        <div class="code-block">
            <div class="code-title">HTML Code:</div>
            <pre>&lt;a href="/" class="btn btn-primary"&gt;Go to Home&lt;/a&gt;
&lt;a href="/pets/" class="btn btn-secondary"&gt;&lt;i class="fas fa-paw"&gt;&lt;/i&gt; View Pets&lt;/a&gt;
&lt;a href="/shop/" class="btn btn-tertiary btn-outline"&gt;Shop Now&lt;/a&gt;</pre>
        </div>
    </section>

    <!-- Custom Attributes -->
    <section class="example-section">
        <h2 class="example-title">Custom Attributes</h2>
        <p class="example-description">Buttons can have custom attributes for JavaScript interactions.</p>

        <div class="example-container">
            <button class="btn btn-primary" data-toggle="modal" data-target="#exampleModal">Toggle Modal</button>
            <button class="btn btn-success" data-product-id="123" id="add-to-cart-btn">Add to Cart</button>
        </div>

        <div class="code-block">
            <div class="code-title">HTML Code:</div>
            <pre>&lt;button class="btn btn-primary" data-toggle="modal" data-target="#exampleModal"&gt;Toggle Modal&lt;/button&gt;
&lt;button class="btn btn-success" data-product-id="123" id="add-to-cart-btn"&gt;Add to Cart&lt;/button&gt;</pre>
        </div>
    </section>

    <!-- Legacy Button Classes -->
    <section class="example-section">
        <h2 class="example-title">Legacy Button Classes (For Reference)</h2>
        <p class="example-description">These classes are maintained for backward compatibility but new code should use the button component.</p>

        <div class="example-container">
            <a href="#" class="primary-lg-fill">Primary Large Fill</a>
            <a href="#" class="primary-lg-outline">Primary Large Outline</a>
            <a href="#" class="primary-sm-fill">Primary Small Fill</a>
            <a href="#" class="primary-sm-outline">Primary Small Outline</a>
        </div>

        <div class="code-block">
            <div class="code-title">HTML Code:</div>
            <pre>&lt;a href="#" class="primary-lg-fill"&gt;Primary Large Fill&lt;/a&gt;
&lt;a href="#" class="primary-lg-outline"&gt;Primary Large Outline&lt;/a&gt;
&lt;a href="#" class="primary-sm-fill"&gt;Primary Small Fill&lt;/a&gt;
&lt;a href="#" class="primary-sm-outline"&gt;Primary Small Outline&lt;/a&gt;</pre>
        </div>
    </section>
</div>
{% endblock %}
