/* Navigation Styles */

/* Main Header */
.main-header {
  background-color: var(--white);
  box-shadow: var(--shadow-base);
  position: sticky;
  top: 0;
  z-index: var(--z-50);
  padding: var(--spacing-sm) 0;
  width: 100%;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: var(--spacing-base);
}

/* Logo */
.logo {
  display: flex;
  align-items: center;
}

.logo a {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-xl);
  font-weight: var(--fw-bold);
  color: var(--primary);
}

.logo img {
  height: 40px;
}

/* Search Bar */
.search-bar {
  flex: 1;
  max-width: 400px;
  position: relative;
}

.search-input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-lg);
  padding-right: 40px;
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-full);
  font-size: var(--font-sm);
  transition: var(--transition-base);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px var(--primary-light);
}

.search-button {
  position: absolute;
  right: 5px;
  top: 50%;
  transform: translateY(-50%);
  background-color: transparent;
  border: none;
  color: var(--gray-500);
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-base);
}

.search-button:hover {
  color: var(--primary);
  background-color: var(--gray-100);
}

/* Main Navigation */
.main-nav ul {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: var(--spacing-lg);
}

.main-nav a {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--text);
  font-weight: var(--fw-medium);
  padding: var(--spacing-sm) var(--spacing-base);
  border-radius: var(--radius-base);
  transition: var(--transition-base);
}

.main-nav a:hover {
  color: var(--primary);
  background-color: var(--primary-light);
}

.main-nav a.active {
  color: var(--primary);
  background-color: var(--primary-light);
}

/* User Actions */
.user-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-base);
}

.cart-icon, .notification-icon, .message-icon {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  color: var(--text);
  transition: var(--transition-base);
}

.cart-icon:hover, .notification-icon:hover, .message-icon:hover {
  background-color: var(--gray-100);
  color: var(--primary);
}

.cart-count, .notification-count, .message-count {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: var(--primary);
  color: var(--white);
  font-size: 10px;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* User Dropdown */
.user-dropdown {
  position: relative;
}

.dropdown-toggle {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--primary-light);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: var(--white);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
  min-width: 200px;
  z-index: var(--z-50);
  padding: var(--spacing-sm);
  display: none;
}

.dropdown-menu a {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-base);
  color: var(--text);
  font-size: var(--font-sm);
  border-radius: var(--radius-sm);
  transition: var(--transition-base);
}

.dropdown-menu a:hover {
  background-color: var(--gray-100);
  color: var(--primary);
}

.user-dropdown:hover .dropdown-menu {
  display: block;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
  display: none;
  background: none;
  border: none;
  color: var(--text);
  font-size: var(--font-lg);
  cursor: pointer;
}

/* Mobile Menu */
.mobile-menu {
  position: fixed;
  top: 0;
  right: -300px;
  width: 300px;
  height: 100vh;
  background-color: var(--white);
  z-index: var(--z-50);
  box-shadow: var(--shadow-lg);
  transition: right 0.3s ease-in-out;
  overflow-y: auto;
}

.mobile-menu.active {
  right: 0;
}

.mobile-menu-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-base);
  border-bottom: 1px solid var(--gray-200);
}

.mobile-menu-close {
  background: none;
  border: none;
  color: var(--text);
  font-size: var(--font-lg);
  cursor: pointer;
}

.mobile-search {
  padding: var(--spacing-base);
  border-bottom: 1px solid var(--gray-200);
}

.mobile-nav ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.mobile-nav a {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-base);
  color: var(--text);
  border-bottom: 1px solid var(--gray-200);
  transition: var(--transition-base);
}

.mobile-nav a:hover, .mobile-nav a.active {
  background-color: var(--gray-100);
  color: var(--primary);
}

.mobile-nav i {
  width: 20px;
  text-align: center;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .main-nav {
    display: none;
  }
  
  .mobile-menu-toggle {
    display: block;
  }
  
  .search-bar {
    order: 3;
    max-width: 100%;
    width: 100%;
    margin-top: var(--spacing-sm);
  }
  
  .header-content {
    flex-wrap: wrap;
  }
}

@media (max-width: 768px) {
  .user-actions .cart-icon,
  .user-actions .notification-icon,
  .user-actions .message-icon {
    display: none;
  }
}
