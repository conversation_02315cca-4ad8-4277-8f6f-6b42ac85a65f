/* Base Styles */
*, *::before, *::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family-sans);
  font-size: var(--font-base);
  line-height: var(--line-height-base);
  color: var(--text);
  background-color: var(--background);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  margin-bottom: var(--spacing-base);
  font-weight: var(--fw-semibold);
  line-height: var(--line-height-tight);
  color: var(--text);
}

h1 {
  font-size: var(--font-4xl);
}

h2 {
  font-size: var(--font-3xl);
}

h3 {
  font-size: var(--font-2xl);
}

h4 {
  font-size: var(--font-xl);
}

h5 {
  font-size: var(--font-lg);
}

h6 {
  font-size: var(--font-md);
}

p {
  margin-bottom: var(--spacing-base);
}

a {
  color: var(--primary);
  text-decoration: none;
  transition: var(--transition-base);
}

a:hover {
  color: var(--primary-dark);
}

ul, ol {
  margin-bottom: var(--spacing-base);
  padding-left: var(--spacing-xl);
}

/* Container */
.container {
  width: 100%;
  max-width: var(--container-xl);
  margin: 0 auto;
  padding: 0 var(--spacing-base);
}

/* Header */
.main-header {
  background-color: var(--white);
  box-shadow: var(--shadow-base);
  position: sticky;
  top: 0;
  z-index: var(--z-50);
  padding: var(--spacing-sm) 0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: var(--spacing-base);
}

.logo {
  display: flex;
  align-items: center;
}

.logo a {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-xl);
  font-weight: var(--fw-bold);
  color: var(--primary);
}

.logo img {
  height: 40px;
}

/* Navigation */
.main-nav ul {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: var(--spacing-lg);
}

.main-nav a {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--text);
  font-weight: var(--fw-medium);
  padding: var(--spacing-sm) var(--spacing-base);
  border-radius: var(--radius-base);
  transition: var(--transition-base);
}

.main-nav a:hover {
  color: var(--primary);
  background-color: var(--primary-light);
}

.main-nav a.active {
  color: var(--primary);
  background-color: var(--primary-light);
}

/* User Actions */
.user-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.cart-icon {
  position: relative;
}

.cart-count {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: var(--primary);
  color: var(--white);
  font-size: var(--font-xs);
  font-weight: var(--fw-bold);
  width: 20px;
  height: 20px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-dropdown {
  position: relative;
}

.dropdown-toggle {
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  object-fit: cover;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: var(--white);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  min-width: 200px;
  padding: var(--spacing-sm);
  z-index: var(--z-50);
  display: none;
}

.dropdown-menu.show {
  display: block;
}

.dropdown-menu a {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-base);
  color: var(--text);
  border-radius: var(--radius-base);
}

.dropdown-menu a:hover {
  background-color: var(--gray-100);
}

/* Mobile Menu */
.mobile-menu-toggle {
  display: none;
  background: none;
  border: none;
  font-size: var(--font-xl);
  color: var(--text);
  cursor: pointer;
}

.mobile-menu {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--white);
  z-index: var(--z-50);
  transform: translateX(-100%);
  transition: transform 0.3s ease-in-out;
  overflow-y: auto;
}

.mobile-menu.show {
  transform: translateX(0);
}

/* Main Content */
.main-content {
  flex: 1;
  padding: var(--spacing-2xl) 0;
}

/* Footer */
.main-footer {
  background-color: var(--dark);
  color: var(--white);
  padding: var(--spacing-4xl) 0 var(--spacing-2xl);
  margin-top: auto;
}

.footer-content {
  display: grid;
  grid-template-columns: var(--grid-cols-4);
  gap: var(--gap-2xl);
}

.footer-logo {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.footer-logo img {
  height: 40px;
}

.footer-section h4 {
  color: var(--white);
  margin-bottom: var(--spacing-lg);
  font-size: var(--font-lg);
}

.footer-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-section li {
  margin-bottom: var(--spacing-sm);
}

.footer-section a {
  color: var(--gray-300);
  transition: var(--transition-base);
}

.footer-section a:hover {
  color: var(--white);
}

.footer-bottom {
  margin-top: var(--spacing-4xl);
  padding-top: var(--spacing-xl);
  border-top: 1px solid var(--gray-700);
  text-align: center;
  color: var(--gray-400);
  font-size: var(--font-sm);
}
