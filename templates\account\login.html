{% extends "base.html" %}
{% load i18n %}
{% load account socialaccount %}

{% block title %}{% trans "Sign In" %} | PetPaw{% endblock %}

{% block content %}
<div class="container">
  <div class="auth-container">
    <div class="auth-card">
      <div class="auth-header">
        <h1 class="auth-title">{% trans "Sign In" %}</h1>
        <p class="auth-subtitle">{% trans "Welcome back to PetPaw" %}</p>
      </div>

      <form class="auth-form" method="post" action="{% url 'account_login' %}">
        {% csrf_token %}

        {% if form.non_field_errors %}
        <div class="invalid-feedback">
          {% for error in form.non_field_errors %}
            <p>{{ error }}</p>
          {% endfor %}
        </div>
        {% endif %}

        <div class="form-group">
          <label for="{{ form.login.id_for_label }}" class="form-label">{% trans "Username or Email" %}</label>
          <input type="text" name="{{ form.login.name }}" id="{{ form.login.id_for_label }}"
                 value="{{ form.login.value|default:'' }}"
                 class="form-control {% if form.login.errors %}is-invalid{% endif %}"
                 placeholder="{% trans 'Enter your email or username' %}" />
          {% if form.login.errors %}
          <div class="invalid-feedback">
            {% for error in form.login.errors %}
              <p>{{ error }}</p>
            {% endfor %}
          </div>
          {% endif %}
        </div>

        <div class="form-group">
          <label for="{{ form.password.id_for_label }}" class="form-label">{% trans "Password" %}</label>
          <input type="password" name="{{ form.password.name }}" id="{{ form.password.id_for_label }}"
                 class="form-control {% if form.password.errors %}is-invalid{% endif %}"
                 placeholder="{% trans 'Enter your password' %}" />
          {% if form.password.errors %}
          <div class="invalid-feedback">
            {% for error in form.password.errors %}
              <p>{{ error }}</p>
            {% endfor %}
          </div>
          {% endif %}
        </div>

        <div class="auth-options">
          <div class="form-check">
            <input type="checkbox" name="{{ form.remember.name }}" id="{{ form.remember.id_for_label }}"
                   class="form-check-input" {% if form.remember.value %}checked{% endif %} />
            <label for="{{ form.remember.id_for_label }}" class="form-check-label">
              {% trans "Remember Me" %}
            </label>
          </div>
          <a href="{% url 'account_reset_password' %}" class="forgot-password">{% trans "Forgot Password?" %}</a>
        </div>

        {% if redirect_field_value %}
          <input type="hidden" name="{{ redirect_field_name }}" value="{{ redirect_field_value }}" />
        {% endif %}

        <button type="submit" class="btn btn-primary auth-submit">{% trans "Sign In" %}</button>
      </form>

      {% get_providers as socialaccount_providers %}
      {% if socialaccount_providers %}
      <div class="auth-divider">
        <div class="auth-divider-line"></div>
        <div class="auth-divider-text">{% trans "or sign in with" %}</div>
        <div class="auth-divider-line"></div>
      </div>

      <div class="social-login">
        {% if 'google' in socialaccount_providers %}
        <a href="{% provider_login_url 'google' %}" class="social-button">
          <i class="fab fa-google social-icon"></i>
          <span>{% trans "Continue with Google" %}</span>
        </a>
        {% endif %}
        {% if 'facebook' in socialaccount_providers %}
        <a href="{% provider_login_url 'facebook' %}" class="social-button">
          <i class="fab fa-facebook social-icon"></i>
          <span>{% trans "Continue with Facebook" %}</span>
        </a>
        {% endif %}
      </div>
      {% endif %}

      <div class="auth-footer">
        <p>{% trans "Don't have an account?" %} <a href="{% url 'account_signup' %}">{% trans "Sign Up" %}</a></p>
      </div>
    </div>
  </div>
</div>
{% endblock %}
