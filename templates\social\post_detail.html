{% extends 'base.html' %}

{% block title %}{{ post.user.username }}'s Post | PetPaw{% endblock %}

{% block extra_css %}
<style>
    .post-detail-container {
        max-width: 800px;
        margin: 0 auto;
        padding: var(--spacing-xl) 0;
    }

    .post-card {
        background-color: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-base);
        margin-bottom: var(--spacing-xl);
        overflow: hidden;
    }

    .post-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: var(--spacing-base) var(--spacing-xl);
    }

    .post-user {
        display: flex;
        align-items: center;
        gap: var(--gap-base);
    }

    .post-user-avatar {
        width: 40px;
        height: 40px;
        border-radius: var(--radius-full);
        object-fit: cover;
    }

    .post-user-info {
        display: flex;
        flex-direction: column;
    }

    .post-user-name {
        font-weight: var(--fw-medium);
    }

    .post-time {
        font-size: var(--font-xs);
        color: var(--text-light);
    }

    .post-options {
        background: none;
        border: none;
        cursor: pointer;
        color: var(--text-light);
        font-size: var(--font-lg);
    }

    .post-options-menu {
        position: absolute;
        right: 0;
        top: 100%;
        background-color: var(--white);
        border-radius: var(--radius-md);
        box-shadow: var(--shadow-lg);
        padding: var(--spacing-sm) 0;
        min-width: 150px;
        z-index: 10;
        display: none;
    }

    .post-options-menu.active {
        display: block;
    }

    .post-options-menu a {
        display: block;
        padding: var(--spacing-sm) var(--spacing-base);
        color: var(--text);
        text-decoration: none;
        transition: var(--transition-base);
    }

    .post-options-menu a:hover {
        background-color: var(--gray-100);
    }

    .post-image {
        width: 100%;
        max-height: 600px;
        object-fit: contain;
    }

    .post-content {
        padding: var(--spacing-xl);
    }

    .post-text {
        margin-bottom: var(--spacing-base);
    }

    .post-tags {
        display: flex;
        flex-wrap: wrap;
        gap: var(--gap-xs);
        margin-bottom: var(--spacing-base);
    }

    .post-tag {
        background-color: var(--gray-100);
        color: var(--primary);
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--radius-full);
        font-size: var(--font-xs);
        text-decoration: none;
    }

    .post-tag:hover {
        background-color: var(--primary-light);
    }

    .post-actions {
        padding: 0 var(--spacing-xl);
        border-top: 1px solid var(--gray-200);
    }

    .post-action-buttons {
        display: flex;
        gap: var(--gap-lg);
        padding: var(--spacing-base) 0;
    }

    .post-action-button {
        background: none;
        border: none;
        display: flex;
        align-items: center;
        gap: var(--gap-xs);
        color: var(--text);
        cursor: pointer;
        font-size: var(--font-base);
    }

    .post-action-button.liked {
        color: var(--danger);
    }

    .post-action-button:hover {
        color: var(--primary);
    }

    .post-action-button.liked:hover {
        color: var(--danger);
    }

    .post-likes {
        padding: 0 var(--spacing-xl) var(--spacing-base);
        font-weight: var(--fw-medium);
    }

    .post-comments {
        padding: var(--spacing-base) var(--spacing-xl);
        border-top: 1px solid var(--gray-200);
    }

    .post-comment {
        display: flex;
        gap: var(--gap-base);
        margin-bottom: var(--spacing-base);
    }

    .comment-avatar {
        width: 32px;
        height: 32px;
        border-radius: var(--radius-full);
        object-fit: cover;
    }

    .comment-content {
        flex: 1;
    }

    .comment-user {
        font-weight: var(--fw-medium);
        margin-right: var(--spacing-xs);
    }

    .comment-time {
        font-size: var(--font-xs);
        color: var(--text-light);
        margin-top: var(--spacing-xs);
    }

    .post-comment-form {
        display: flex;
        align-items: center;
        gap: var(--gap-base);
        padding: var(--spacing-base) var(--spacing-xl) var(--spacing-xl);
        border-top: 1px solid var(--gray-200);
    }

    .comment-input {
        flex: 1;
        border: none;
        background-color: var(--gray-100);
        border-radius: var(--radius-full);
        padding: var(--spacing-sm) var(--spacing-base);
    }

    .comment-input:focus {
        outline: none;
    }

    .comment-submit {
        background: none;
        border: none;
        color: var(--primary);
        font-weight: var(--fw-medium);
        cursor: pointer;
    }

    .comment-submit:disabled {
        color: var(--gray-400);
        cursor: not-allowed;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="post-detail-container">
        <div class="post-card">
            <div class="post-header">
                <div class="post-user">
                    <img src="{{ post.user.profile_picture.url }}" alt="{{ post.user.username }}" class="post-user-avatar">
                    <div class="post-user-info">
                        <div class="post-user-name">{{ post.user.username }}</div>
                        <div class="post-time">{{ post.created_at|timesince }} ago</div>
                    </div>
                </div>

                {% if user == post.user %}
                <div style="position: relative;">
                    <button type="button" class="post-options" id="post-options-btn">
                        <i class="fas fa-ellipsis-h"></i>
                    </button>
                    <div class="post-options-menu" id="post-options-menu">
                        <a href="{% url 'post-update' pk=post.pk %}">
                            <i class="fas fa-edit"></i> Edit Post
                        </a>
                        <a href="{% url 'post-delete' pk=post.pk %}">
                            <i class="fas fa-trash-alt"></i> Delete Post
                        </a>
                    </div>
                </div>
                {% endif %}
            </div>

            {% if post.image %}
                <img src="{{ post.image.url }}" alt="{{ post.content }}" class="post-image">
            {% endif %}

            {% if post.video %}
                <video src="{{ post.video.url }}" controls class="post-image"></video>
            {% endif %}

            <div class="post-content">
                <div class="post-text">{{ post.content }}</div>

                {% if post.hashtags.all %}
                    <div class="post-tags">
                        {% for tag in post.hashtags.all %}
                            <a href="#" class="post-tag">#{{ tag.name }}</a>
                        {% endfor %}
                    </div>
                {% endif %}

                {% if post.pet %}
                    <div class="post-pet">
                        <a href="{% url 'pet-detail' pk=post.pet.pk %}">
                            <i class="fas fa-paw"></i> {{ post.pet.name }}
                        </a>
                    </div>
                {% endif %}
            </div>

            <div class="post-actions">
                <div class="post-action-buttons">
                    <form method="post" action="{% url 'like-post' pk=post.pk %}" class="like-form">
                        {% csrf_token %}
                        <button type="submit" class="post-action-button {% if has_liked %}liked{% endif %}">
                            <i class="{% if has_liked %}fas{% else %}far{% endif %} fa-heart"></i>
                            <span>Like</span>
                        </button>
                    </form>

                    <button type="button" class="post-action-button" id="comment-btn">
                        <i class="far fa-comment"></i>
                        <span>Comment</span>
                    </button>

                    <button type="button" class="post-action-button">
                        <i class="far fa-share-square"></i>
                        <span>Share</span>
                    </button>
                </div>
            </div>

            <div class="post-likes">
                {{ post.likes.count }} likes
            </div>

            <div class="post-comments">
                <h3>Comments ({{ comments|length }})</h3>

                {% for comment in comments %}
                    <div class="post-comment">
                        <img src="{{ comment.user.profile_picture.url }}" alt="{{ comment.user.username }}" class="comment-avatar">
                        <div class="comment-content">
                            <div>
                                <span class="comment-user">{{ comment.user.username }}</span>
                                <span class="comment-text">{{ comment.content }}</span>
                            </div>
                            <div class="comment-time">{{ comment.created_at|timesince }} ago</div>
                        </div>
                    </div>
                {% empty %}
                    <p>No comments yet. Be the first to comment!</p>
                {% endfor %}
            </div>

            <form method="post" action="{% url 'add-comment' pk=post.pk %}" class="post-comment-form">
                {% csrf_token %}
                <img src="{{ user.profile_picture.url }}" alt="{{ user.username }}" class="comment-avatar">
                <input type="text" name="content" placeholder="Add a comment..." class="comment-input" required>
                <button type="submit" class="comment-submit">Post</button>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Post options menu toggle
        const optionsBtn = document.getElementById('post-options-btn');
        const optionsMenu = document.getElementById('post-options-menu');

        if (optionsBtn && optionsMenu) {
            optionsBtn.addEventListener('click', function(e) {
                e.stopPropagation();
                optionsMenu.classList.toggle('active');
            });

            // Close menu when clicking outside
            document.addEventListener('click', function() {
                optionsMenu.classList.remove('active');
            });
        }

        // Focus comment input when comment button is clicked
        const commentBtn = document.getElementById('comment-btn');
        const commentInput = document.querySelector('.comment-input');

        if (commentBtn && commentInput) {
            commentBtn.addEventListener('click', function() {
                commentInput.focus();
            });
        }

        // Comment input validation
        const commentForm = document.querySelector('.post-comment-form');

        if (commentForm) {
            const input = commentForm.querySelector('.comment-input');
            const submitBtn = commentForm.querySelector('.comment-submit');

            if (input && submitBtn) {
                input.addEventListener('input', function() {
                    submitBtn.disabled = this.value.trim() === '';
                });

                // Initial state
                submitBtn.disabled = input.value.trim() === '';
            }
        }
    });
</script>
{% endblock %}
