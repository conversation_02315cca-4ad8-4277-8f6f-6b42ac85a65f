{% extends 'base.html' %}

{% block title %}Notifications | PetPaw{% endblock %}

{% block extra_css %}
<style>
    .notifications-container {
        max-width: 800px;
        margin: 0 auto;
        padding: var(--spacing-xl) 0;
    }

    .notifications-header {
        margin-bottom: var(--spacing-xl);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .notifications-title {
        font-size: var(--font-2xl);
    }

    .notifications-card {
        background-color: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-base);
        overflow: hidden;
    }

    .notification-item {
        padding: var(--spacing-lg);
        border-bottom: 1px solid var(--gray-200);
        display: flex;
        align-items: center;
        gap: var(--gap-base);
        transition: var(--transition-base);
    }

    .notification-item:last-child {
        border-bottom: none;
    }

    .notification-item:hover {
        background-color: var(--gray-50);
    }

    .notification-avatar {
        width: 50px;
        height: 50px;
        border-radius: var(--radius-full);
        object-fit: cover;
    }

    .notification-content {
        flex: 1;
    }

    .notification-message {
        margin-bottom: var(--spacing-xs);
    }

    .notification-message a {
        font-weight: var(--fw-medium);
        color: var(--text);
        text-decoration: none;
    }

    .notification-message a:hover {
        color: var(--primary);
    }

    .notification-time {
        font-size: var(--font-xs);
        color: var(--text-light);
    }

    .notification-icon {
        width: 40px;
        height: 40px;
        border-radius: var(--radius-full);
        background-color: var(--gray-100);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--text);
    }

    .notification-icon.like {
        background-color: rgba(var(--danger-rgb), 0.1);
        color: var(--danger);
    }

    .notification-icon.comment {
        background-color: rgba(var(--primary-rgb), 0.1);
        color: var(--primary);
    }

    .notification-icon.follow {
        background-color: rgba(var(--success-rgb), 0.1);
        color: var(--success);
    }

    .notification-icon.mention {
        background-color: rgba(var(--warning-rgb), 0.1);
        color: var(--warning);
    }

    .notification-icon.message {
        background-color: rgba(var(--info-rgb), 0.1);
        color: var(--info);
    }

    .notification-icon.story_like {
        background-color: rgba(var(--danger-rgb), 0.1);
        color: var(--danger);
    }

    .notification-icon.story_comment {
        background-color: rgba(var(--primary-rgb), 0.1);
        color: var(--primary);
    }

    .story-thumbnail-small {
        width: 40px;
        height: 40px;
        border-radius: var(--radius-base);
        object-fit: cover;
        margin-left: var(--spacing-sm);
    }

    .notification-unread {
        width: 10px;
        height: 10px;
        border-radius: var(--radius-full);
        background-color: var(--primary);
    }

    .empty-state {
        padding: var(--spacing-2xl);
        text-align: center;
    }

    .empty-state p {
        margin-bottom: var(--spacing-base);
        color: var(--text-light);
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="notifications-container">
        <div class="notifications-header">
            <h1 class="notifications-title">Notifications</h1>

            <a href="{% url 'feed' %}" class="btn btn-outline">
                <i class="fas fa-arrow-left"></i> Back to Feed
            </a>
        </div>

        <div class="notifications-card">
            {% for notification in notifications %}
                <div class="notification-item">
                    <img src="{{ notification.sender.profile_picture.url }}" alt="{{ notification.sender.username }}" class="notification-avatar">

                    <div class="notification-content">
                        <div class="notification-message">
                            {% if notification.notification_type == 'like' %}
                                <a href="{% url 'user-profile' username=notification.sender.username %}">{{ notification.sender.username }}</a> liked your <a href="{% url 'post-detail' pk=notification.post.pk %}">post</a>.
                            {% elif notification.notification_type == 'comment' %}
                                <a href="{% url 'user-profile' username=notification.sender.username %}">{{ notification.sender.username }}</a> commented on your <a href="{% url 'post-detail' pk=notification.post.pk %}">post</a>.
                            {% elif notification.notification_type == 'story_like' %}
                                <a href="{% url 'user-profile' username=notification.sender.username %}">{{ notification.sender.username }}</a> liked your <a href="{% url 'view-story' pk=notification.story.pk %}">story</a>.
                            {% elif notification.notification_type == 'story_comment' %}
                                <a href="{% url 'user-profile' username=notification.sender.username %}">{{ notification.sender.username }}</a> commented on your <a href="{% url 'view-story' pk=notification.story.pk %}">story</a>: "{{ notification.story_comment_text|truncatechars:50 }}"
                            {% elif notification.notification_type == 'follow' %}
                                <a href="{% url 'user-profile' username=notification.sender.username %}">{{ notification.sender.username }}</a> started following you.
                            {% elif notification.notification_type == 'mention' %}
                                <a href="{% url 'user-profile' username=notification.sender.username %}">{{ notification.sender.username }}</a> mentioned you in a <a href="{% url 'post-detail' pk=notification.post.pk %}">post</a>.
                            {% elif notification.notification_type == 'message' %}
                                <a href="{% url 'user-profile' username=notification.sender.username %}">{{ notification.sender.username }}</a> sent you a <a href="{% url 'conversation-detail' pk=notification.conversation.pk %}">message</a>.
                            {% else %}
                                {{ notification.message }}
                            {% endif %}
                        </div>
                        <div class="notification-time">{{ notification.created_at|timesince }} ago</div>
                    </div>

                    {% if notification.notification_type == 'story_like' or notification.notification_type == 'story_comment' %}
                        <!-- Show story thumbnail for story notifications -->
                        {% if notification.story.image %}
                            <img src="{{ notification.story.image.url }}" alt="Story" class="story-thumbnail-small">
                        {% elif notification.story.video %}
                            <video src="{{ notification.story.video.url }}" class="story-thumbnail-small" muted>
                                <div class="notification-icon {{ notification.notification_type }}">
                                    <i class="fas fa-play"></i>
                                </div>
                            </video>
                        {% else %}
                            <div class="notification-icon {{ notification.notification_type }}">
                                <i class="fas fa-image"></i>
                            </div>
                        {% endif %}
                    {% else %}
                        <div class="notification-icon {{ notification.notification_type }}">
                            {% if notification.notification_type == 'like' %}
                                <i class="fas fa-heart"></i>
                            {% elif notification.notification_type == 'comment' %}
                                <i class="fas fa-comment"></i>
                            {% elif notification.notification_type == 'follow' %}
                                <i class="fas fa-user-plus"></i>
                            {% elif notification.notification_type == 'mention' %}
                                <i class="fas fa-at"></i>
                            {% elif notification.notification_type == 'message' %}
                                <i class="fas fa-envelope"></i>
                            {% endif %}
                        </div>
                    {% endif %}
                </div>
            {% empty %}
                <div class="empty-state">
                    <p>You don't have any notifications yet.</p>
                    <p>Interact with other users to receive notifications.</p>
                </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endblock %}
