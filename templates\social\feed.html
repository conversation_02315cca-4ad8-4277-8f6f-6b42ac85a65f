{% extends 'base.html' %}

{% block title %}Feed | PetPaw{% endblock %}

{% block extra_css %}
<style>
    .feed-container {
        display: grid;
        grid-template-columns: 1fr 2fr 1fr;
        gap: var(--gap-xl);
    }
    
    @media (max-width: 992px) {
        .feed-container {
            grid-template-columns: 1fr;
        }
    }
    
    .sidebar {
        position: sticky;
        top: calc(var(--spacing-2xl) + 60px);
        height: fit-content;
    }
    
    .sidebar-card {
        background-color: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-base);
        padding: var(--spacing-xl);
        margin-bottom: var(--spacing-xl);
    }
    
    .sidebar-title {
        font-size: var(--font-lg);
        margin-bottom: var(--spacing-lg);
        padding-bottom: var(--spacing-sm);
        border-bottom: 1px solid var(--gray-200);
    }
    
    .user-profile-card {
        display: flex;
        align-items: center;
        gap: var(--gap-base);
        margin-bottom: var(--spacing-lg);
    }
    
    .user-avatar {
        width: 60px;
        height: 60px;
        border-radius: var(--radius-full);
        object-fit: cover;
    }
    
    .user-info {
        flex: 1;
    }
    
    .user-name {
        font-weight: var(--fw-medium);
        margin-bottom: var(--spacing-xs);
    }
    
    .user-stats {
        display: flex;
        gap: var(--gap-base);
        font-size: var(--font-sm);
        color: var(--text-light);
    }
    
    .create-post-button {
        width: 100%;
        margin-top: var(--spacing-base);
    }
    
    .trending-tags {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    
    .trending-tag-item {
        margin-bottom: var(--spacing-sm);
    }
    
    .trending-tag-link {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: var(--spacing-sm) 0;
        color: var(--text);
        transition: var(--transition-base);
    }
    
    .trending-tag-link:hover {
        color: var(--primary);
    }
    
    .trending-tag-count {
        background-color: var(--gray-200);
        color: var(--text);
        font-size: var(--font-xs);
        padding: 2px 8px;
        border-radius: var(--radius-full);
    }
    
    .suggested-accounts {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    
    .suggested-account-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: var(--spacing-base);
    }
    
    .suggested-account-info {
        display: flex;
        align-items: center;
        gap: var(--gap-sm);
    }
    
    .suggested-account-avatar {
        width: 40px;
        height: 40px;
        border-radius: var(--radius-full);
        object-fit: cover;
    }
    
    .suggested-account-name {
        font-weight: var(--fw-medium);
        font-size: var(--font-sm);
    }
    
    .follow-button {
        font-size: var(--font-xs);
        padding: var(--spacing-xs) var(--spacing-sm);
    }
    
    .stories-container {
        display: flex;
        gap: var(--gap-sm);
        overflow-x: auto;
        padding: var(--spacing-sm) 0;
        margin-bottom: var(--spacing-xl);
    }
    
    .story-item {
        flex: 0 0 80px;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: var(--gap-xs);
    }
    
    .story-avatar {
        width: 60px;
        height: 60px;
        border-radius: var(--radius-full);
        padding: 3px;
        background: linear-gradient(45deg, var(--primary), var(--secondary));
    }
    
    .story-avatar img {
        width: 100%;
        height: 100%;
        border-radius: var(--radius-full);
        object-fit: cover;
        border: 2px solid var(--white);
    }
    
    .story-username {
        font-size: var(--font-xs);
        text-align: center;
        max-width: 80px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    
    .create-story {
        position: relative;
    }
    
    .create-story-icon {
        position: absolute;
        bottom: 0;
        right: 0;
        background-color: var(--primary);
        color: var(--white);
        width: 20px;
        height: 20px;
        border-radius: var(--radius-full);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10px;
        border: 2px solid var(--white);
    }
    
    .post-card {
        background-color: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-base);
        margin-bottom: var(--spacing-xl);
        overflow: hidden;
    }
    
    .post-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: var(--spacing-base) var(--spacing-xl);
    }
    
    .post-user {
        display: flex;
        align-items: center;
        gap: var(--gap-base);
    }
    
    .post-user-avatar {
        width: 40px;
        height: 40px;
        border-radius: var(--radius-full);
        object-fit: cover;
    }
    
    .post-user-info {
        display: flex;
        flex-direction: column;
    }
    
    .post-user-name {
        font-weight: var(--fw-medium);
    }
    
    .post-time {
        font-size: var(--font-xs);
        color: var(--text-light);
    }
    
    .post-options {
        background: none;
        border: none;
        cursor: pointer;
        color: var(--text-light);
        font-size: var(--font-lg);
    }
    
    .post-image {
        width: 100%;
        max-height: 500px;
        object-fit: cover;
    }
    
    .post-content {
        padding: var(--spacing-xl);
    }
    
    .post-text {
        margin-bottom: var(--spacing-base);
    }
    
    .post-tags {
        display: flex;
        flex-wrap: wrap;
        gap: var(--gap-xs);
        margin-bottom: var(--spacing-base);
    }
    
    .post-tag {
        background-color: var(--primary-light);
        color: var(--primary);
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--radius-full);
        font-size: var(--font-xs);
    }
    
    .post-actions {
        display: flex;
        justify-content: space-between;
        padding: 0 var(--spacing-xl) var(--spacing-base);
        border-bottom: 1px solid var(--gray-200);
    }
    
    .post-action-buttons {
        display: flex;
        gap: var(--gap-lg);
    }
    
    .post-action-button {
        background: none;
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: var(--gap-xs);
        color: var(--text-light);
        transition: var(--transition-base);
    }
    
    .post-action-button:hover {
        color: var(--primary);
    }
    
    .post-action-button.liked {
        color: var(--danger);
    }
    
    .post-action-button.liked:hover {
        color: var(--danger);
    }
    
    .post-stats {
        display: flex;
        justify-content: space-between;
        padding: var(--spacing-base) var(--spacing-xl);
        font-size: var(--font-sm);
        color: var(--text-light);
    }
    
    .post-comments {
        padding: 0 var(--spacing-xl) var(--spacing-base);
    }
    
    .post-comment {
        display: flex;
        gap: var(--gap-base);
        margin-bottom: var(--spacing-base);
    }
    
    .comment-avatar {
        width: 32px;
        height: 32px;
        border-radius: var(--radius-full);
        object-fit: cover;
    }
    
    .comment-content {
        flex: 1;
    }
    
    .comment-user {
        font-weight: var(--fw-medium);
        margin-right: var(--spacing-xs);
    }
    
    .comment-text {
        display: inline;
    }
    
    .comment-time {
        font-size: var(--font-xs);
        color: var(--text-light);
        margin-top: var(--spacing-xs);
    }
    
    .post-comment-form {
        display: flex;
        align-items: center;
        gap: var(--gap-base);
        padding: var(--spacing-base) var(--spacing-xl) var(--spacing-xl);
    }
    
    .comment-input {
        flex: 1;
        border: none;
        background-color: var(--gray-100);
        border-radius: var(--radius-full);
        padding: var(--spacing-sm) var(--spacing-base);
    }
    
    .comment-input:focus {
        outline: none;
    }
    
    .comment-submit {
        background: none;
        border: none;
        color: var(--primary);
        font-weight: var(--fw-medium);
        cursor: pointer;
    }
    
    .comment-submit:disabled {
        color: var(--gray-400);
        cursor: not-allowed;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="feed-container">
        <div class="sidebar left-sidebar">
            <div class="sidebar-card">
                <div class="user-profile-card">
                    <img src="{{ user.profile_picture.url }}" alt="{{ user.username }}" class="user-avatar">
                    <div class="user-info">
                        <div class="user-name">{{ user.username }}</div>
                        <div class="user-stats">
                            <span>{{ user.profile.get_followers_count }} followers</span>
                            <span>{{ user.profile.get_following_count }} following</span>
                        </div>
                    </div>
                </div>
                
                <a href="{% url 'post-create' %}" class="btn btn-primary create-post-button">
                    <i class="fas fa-plus"></i> Create Post
                </a>
            </div>
            
            <div class="sidebar-card">
                <h3 class="sidebar-title">Trending Tags</h3>
                <ul class="trending-tags">
                    <li class="trending-tag-item">
                        <a href="#" class="trending-tag-link">
                            #DogsOfPetPaw
                            <span class="trending-tag-count">2.5k</span>
                        </a>
                    </li>
                    <li class="trending-tag-item">
                        <a href="#" class="trending-tag-link">
                            #CuteCats
                            <span class="trending-tag-count">1.8k</span>
                        </a>
                    </li>
                    <li class="trending-tag-item">
                        <a href="#" class="trending-tag-link">
                            #PetLife
                            <span class="trending-tag-count">1.2k</span>
                        </a>
                    </li>
                    <li class="trending-tag-item">
                        <a href="#" class="trending-tag-link">
                            #AdoptDontShop
                            <span class="trending-tag-count">950</span>
                        </a>
                    </li>
                    <li class="trending-tag-item">
                        <a href="#" class="trending-tag-link">
                            #PetCare
                            <span class="trending-tag-count">820</span>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
        
        <div class="feed-content">
            <div class="stories-container">
                <a href="{% url 'story-create' %}" class="story-item create-story">
                    <div class="story-avatar">
                        <img src="{{ user.profile_picture.url }}" alt="{{ user.username }}">
                        <div class="create-story-icon">
                            <i class="fas fa-plus"></i>
                        </div>
                    </div>
                    <div class="story-username">Add Story</div>
                </a>
                
                {% for story in stories %}
                    <a href="{% url 'view-story' pk=story.pk %}" class="story-item">
                        <div class="story-avatar">
                            <img src="{{ story.user.profile_picture.url }}" alt="{{ story.user.username }}">
                        </div>
                        <div class="story-username">{{ story.user.username }}</div>
                    </a>
                {% endfor %}
            </div>
            
            {% for post in posts %}
                <div class="post-card">
                    <div class="post-header">
                        <div class="post-user">
                            <img src="{{ post.user.profile_picture.url }}" alt="{{ post.user.username }}" class="post-user-avatar">
                            <div class="post-user-info">
                                <div class="post-user-name">{{ post.user.username }}</div>
                                <div class="post-time">{{ post.created_at|timesince }} ago</div>
                            </div>
                        </div>
                        
                        <button type="button" class="post-options">
                            <i class="fas fa-ellipsis-h"></i>
                        </button>
                    </div>
                    
                    {% if post.image %}
                        <img src="{{ post.image.url }}" alt="{{ post.caption }}" class="post-image">
                    {% endif %}
                    
                    <div class="post-content">
                        <p class="post-text">{{ post.caption }}</p>
                        
                        {% if post.tags.all %}
                            <div class="post-tags">
                                {% for tag in post.tags.all %}
                                    <a href="#" class="post-tag">#{{ tag.name }}</a>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="post-actions">
                        <div class="post-action-buttons">
                            <form method="post" action="{% url 'like-post' pk=post.pk %}">
                                {% csrf_token %}
                                <button type="submit" class="post-action-button {% if user in post.likes.all %}liked{% endif %}">
                                    <i class="{% if user in post.likes.all %}fas{% else %}far{% endif %} fa-heart"></i>
                                    <span>{{ post.likes.count }}</span>
                                </button>
                            </form>
                            
                            <a href="{% url 'post-detail' pk=post.pk %}" class="post-action-button">
                                <i class="far fa-comment"></i>
                                <span>{{ post.comments.count }}</span>
                            </a>
                            
                            <button type="button" class="post-action-button">
                                <i class="far fa-share-square"></i>
                                <span>Share</span>
                            </button>
                        </div>
                        
                        <button type="button" class="post-action-button">
                            <i class="far fa-bookmark"></i>
                            <span>Save</span>
                        </button>
                    </div>
                    
                    <div class="post-stats">
                        <div>{{ post.likes.count }} likes</div>
                        <div>{{ post.comments.count }} comments</div>
                    </div>
                    
                    {% if post.comments.all %}
                        <div class="post-comments">
                            {% for comment in post.comments.all|slice:":2" %}
                                <div class="post-comment">
                                    <img src="{{ comment.user.profile_picture.url }}" alt="{{ comment.user.username }}" class="comment-avatar">
                                    <div class="comment-content">
                                        <div>
                                            <span class="comment-user">{{ comment.user.username }}</span>
                                            <span class="comment-text">{{ comment.text }}</span>
                                        </div>
                                        <div class="comment-time">{{ comment.created_at|timesince }} ago</div>
                                    </div>
                                </div>
                            {% endfor %}
                            
                            {% if post.comments.count > 2 %}
                                <a href="{% url 'post-detail' pk=post.pk %}" class="view-more-comments">
                                    View all {{ post.comments.count }} comments
                                </a>
                            {% endif %}
                        </div>
                    {% endif %}
                    
                    <form method="post" action="{% url 'add-comment' pk=post.pk %}" class="post-comment-form">
                        {% csrf_token %}
                        <img src="{{ user.profile_picture.url }}" alt="{{ user.username }}" class="comment-avatar">
                        <input type="text" name="text" placeholder="Add a comment..." class="comment-input" required>
                        <button type="submit" class="comment-submit">Post</button>
                    </form>
                </div>
            {% empty %}
                <div class="empty-state">
                    <p>No posts to show. Follow more users or create your first post!</p>
                    <a href="{% url 'post-create' %}" class="btn btn-primary">Create Post</a>
                </div>
            {% endfor %}
        </div>
        
        <div class="sidebar right-sidebar">
            <div class="sidebar-card">
                <h3 class="sidebar-title">Suggested for You</h3>
                <ul class="suggested-accounts">
                    {% for suggestion in suggested_users %}
                        <li class="suggested-account-item">
                            <div class="suggested-account-info">
                                <img src="{{ suggestion.profile_picture.url }}" alt="{{ suggestion.username }}" class="suggested-account-avatar">
                                <div class="suggested-account-name">{{ suggestion.username }}</div>
                            </div>
                            
                            <a href="{% url 'follow-user' username=suggestion.username %}" class="btn btn-outline btn-sm follow-button">Follow</a>
                        </li>
                    {% endfor %}
                </ul>
            </div>
            
            <div class="sidebar-card">
                <h3 class="sidebar-title">Popular Pets</h3>
                <ul class="suggested-accounts">
                    {% for pet in popular_pets %}
                        <li class="suggested-account-item">
                            <div class="suggested-account-info">
                                <img src="{{ pet.profile_picture.url }}" alt="{{ pet.name }}" class="suggested-account-avatar">
                                <div class="suggested-account-name">{{ pet.name }}</div>
                            </div>
                            
                            <a href="{% url 'follow-pet' pk=pet.pk %}" class="btn btn-outline btn-sm follow-button">Follow</a>
                        </li>
                    {% endfor %}
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Comment input validation
        const commentForms = document.querySelectorAll('.post-comment-form');
        
        commentForms.forEach(form => {
            const input = form.querySelector('.comment-input');
            const submitBtn = form.querySelector('.comment-submit');
            
            if (input && submitBtn) {
                input.addEventListener('input', function() {
                    submitBtn.disabled = this.value.trim() === '';
                });
                
                // Initial state
                submitBtn.disabled = input.value.trim() === '';
            }
        });
        
        // Post options dropdown
        const postOptionsButtons = document.querySelectorAll('.post-options');
        
        postOptionsButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Show options menu (to be implemented)
                console.log('Post options clicked');
            });
        });
    });
</script>
{% endblock %}
