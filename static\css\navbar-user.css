/* User Actions in Navbar */
.user-actions {
    display: flex;
    align-items: center;
    gap: var(--gap-lg);
}

.cart-icon, .notification-icon, .message-icon {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: var(--radius-circle);
    background-color: var(--gray-100);
    color: var(--gray-800);
    transition: var(--transition-base);
}

.cart-icon:hover, .notification-icon:hover, .message-icon:hover {
    background-color: var(--primary);
    color: var(--white);
    text-decoration: none;
}

.cart-count, .notification-count, .message-count {
    position: absolute;
    top: -5px;
    right: -5px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    border-radius: var(--radius-circle);
    background-color: var(--primary);
    color: var(--white);
    font-size: 12px;
    font-weight: var(--fw-bold);
}

.user-dropdown {
    position: relative;
}

.dropdown-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: var(--radius-circle);
    background-color: var(--gray-100);
    border: none;
    cursor: pointer;
    transition: var(--transition-base);
    overflow: hidden;
}

.dropdown-toggle:hover {
    background-color: var(--primary);
}

.dropdown-toggle img.avatar {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    width: 200px;
    background-color: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-md);
    margin-top: var(--spacing-md);
    z-index: var(--z-30);
    display: none;
}

.user-dropdown:hover .dropdown-menu {
    display: block;
}

.dropdown-menu a {
    display: flex;
    align-items: center;
    gap: var(--gap-md);
    padding: var(--spacing-md);
    color: var(--gray-800);
    transition: var(--transition-base);
    text-decoration: none;
}

.dropdown-menu a:hover {
    background-color: var(--gray-100);
    color: var(--primary);
    text-decoration: none;
}

.dropdown-menu a i {
    width: 20px;
    text-align: center;
}

/* Mobile menu toggle */
.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 24px;
    color: var(--gray-800);
    cursor: pointer;
}

/* Mobile menu */
.mobile-menu {
    position: fixed;
    top: 0;
    right: -100%;
    width: 80%;
    max-width: 300px;
    height: 100vh;
    background-color: var(--white);
    box-shadow: var(--shadow-xl);
    z-index: var(--z-50);
    transition: right 0.3s ease-in-out;
    overflow-y: auto;
}

.mobile-menu.active {
    right: 0;
}

.mobile-menu-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-r);
    border-bottom: 1px solid var(--gray-200);
}

.mobile-menu-close {
    background: none;
    border: none;
    font-size: 24px;
    color: var(--gray-800);
    cursor: pointer;
}

.mobile-search {
    padding: var(--spacing-r);
    border-bottom: 1px solid var(--gray-200);
}

.mobile-search form {
    display: flex;
    gap: var(--gap-md);
}

.mobile-search input {
    flex: 1;
    padding: var(--spacing-md) var(--spacing-r);
    border-radius: var(--radius-lg);
    border: 1px solid var(--gray-300);
}

.mobile-search button {
    padding: var(--spacing-md) var(--spacing-r);
    border-radius: var(--radius-lg);
    border: none;
    background-color: var(--primary);
    color: var(--white);
    cursor: pointer;
}

.mobile-nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.mobile-nav ul li {
    border-bottom: 1px solid var(--gray-200);
}

.mobile-nav ul li a {
    display: flex;
    align-items: center;
    gap: var(--gap-md);
    padding: var(--spacing-r);
    color: var(--gray-800);
    text-decoration: none;
}

.mobile-nav ul li a:hover {
    background-color: var(--gray-100);
    color: var(--primary);
}

.mobile-nav ul li a i {
    width: 20px;
    text-align: center;
}

/* Responsive styles */
@media (max-width: 992px) {
    .links {
        display: none;
    }
    
    .user-actions .cart-icon,
    .user-actions .notification-icon,
    .user-actions .message-icon,
    .user-actions .user-dropdown {
        display: none;
    }
    
    .mobile-menu-toggle {
        display: block;
    }
}
