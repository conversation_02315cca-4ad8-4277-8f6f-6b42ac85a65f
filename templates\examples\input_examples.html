{% extends 'base.html' %}

{% block title %}Input Field Examples | PetPaw{% endblock %}

{% block content %}
<div class="container py-5">
    <h1 class="mb-4">Input Field Examples</h1>
    <p class="mb-5">This page demonstrates the various input field styles available in the PetPaw application.</p>

    <div class="card mb-5">
        <div class="card-header">
            <h2 class="h5 mb-0">Basic Text Inputs</h2>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="default-input" class="form-label">Default Input</label>
                        <input type="text" id="default-input" class="form-control" placeholder="Enter text here">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="disabled-input" class="form-label">Disabled Input</label>
                        <input type="text" id="disabled-input" class="form-control" placeholder="This input is disabled" disabled>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="valid-input" class="form-label">Valid Input</label>
                        <input type="text" id="valid-input" class="form-control is-valid" value="Correct input">
                        <div class="form-text">This input has been validated and is correct.</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="invalid-input" class="form-label">Invalid Input</label>
                        <input type="text" id="invalid-input" class="form-control is-invalid" value="Incorrect input">
                        <div class="invalid-feedback">Please provide a valid input.</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card mb-5">
        <div class="card-header">
            <h2 class="h5 mb-0">Input Sizes</h2>
        </div>
        <div class="card-body">
            <div class="form-group mb-4">
                <label for="small-input" class="form-label">Small Input</label>
                <input type="text" id="small-input" class="form-control form-control-sm" placeholder="Small input">
            </div>
            <div class="form-group mb-4">
                <label for="default-size-input" class="form-label">Default Input</label>
                <input type="text" id="default-size-input" class="form-control" placeholder="Default input">
            </div>
            <div class="form-group">
                <label for="large-input" class="form-label">Large Input</label>
                <input type="text" id="large-input" class="form-control form-control-lg" placeholder="Large input">
            </div>
        </div>
    </div>

    <div class="card mb-5">
        <div class="card-header">
            <h2 class="h5 mb-0">Input with Icons</h2>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="email-input" class="form-label">Email Address</label>
                        <div class="input-with-icon">
                            <i class="fas fa-envelope input-icon"></i>
                            <input type="email" id="email-input" class="form-control" placeholder="Enter your email">
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="password-input" class="form-label">Password</label>
                        <div class="input-with-icon">
                            <i class="fas fa-lock input-icon"></i>
                            <input type="password" id="password-input" class="form-control" placeholder="Enter your password">
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="search-input" class="form-label">Search</label>
                        <div class="input-with-icon">
                            <i class="fas fa-search input-icon"></i>
                            <input type="text" id="search-input" class="form-control" placeholder="Search...">
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="calendar-input" class="form-label">Date</label>
                        <div class="input-with-icon">
                            <i class="fas fa-calendar input-icon"></i>
                            <input type="date" id="calendar-input" class="form-control">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card mb-5">
        <div class="card-header">
            <h2 class="h5 mb-0">Textarea</h2>
        </div>
        <div class="card-body">
            <div class="form-group">
                <label for="textarea-example" class="form-label">Message</label>
                <textarea id="textarea-example" class="form-control" rows="4" placeholder="Enter your message here"></textarea>
                <div class="form-text">Please keep your message under 500 characters.</div>
            </div>
        </div>
    </div>

    <div class="card mb-5">
        <div class="card-header">
            <h2 class="h5 mb-0">Checkboxes and Radios</h2>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h3 class="h6 mb-3">Checkboxes</h3>
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="checkbox1">
                        <label class="form-check-label" for="checkbox1">Default checkbox</label>
                    </div>
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="checkbox2" checked>
                        <label class="form-check-label" for="checkbox2">Checked checkbox</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="checkbox3" disabled>
                        <label class="form-check-label" for="checkbox3">Disabled checkbox</label>
                    </div>
                </div>
                <div class="col-md-6">
                    <h3 class="h6 mb-3">Radio Buttons</h3>
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="radio" name="radioExample" id="radio1">
                        <label class="form-check-label" for="radio1">Default radio</label>
                    </div>
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="radio" name="radioExample" id="radio2" checked>
                        <label class="form-check-label" for="radio2">Selected radio</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="radioExample" id="radio3" disabled>
                        <label class="form-check-label" for="radio3">Disabled radio</label>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h2 class="h5 mb-0">Search Input</h2>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="search-example" class="form-label">Search</label>
                        <div class="input-with-icon">
                            <input type="text" id="search-example" class="search-input" placeholder="Search...">
                            <button type="button" class="search-button"><i class="fas fa-search"></i></button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
