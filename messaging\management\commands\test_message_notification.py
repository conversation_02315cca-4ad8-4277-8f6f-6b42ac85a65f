from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from messaging.models import Conversation, Message
from social.models import Notification

User = get_user_model()


class Command(BaseCommand):
    help = 'Create a test message notification'

    def handle(self, *args, **options):
        # Get or create test users
        user1, created = User.objects.get_or_create(
            username='testuser1',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Test',
                'last_name': 'User1'
            }
        )
        if created:
            user1.set_password('testpass123')
            user1.save()
            self.stdout.write(f'Created user: {user1.username}')

        user2, created = User.objects.get_or_create(
            username='testuser2',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Test',
                'last_name': 'User2'
            }
        )
        if created:
            user2.set_password('testpass123')
            user2.save()
            self.stdout.write(f'Created user: {user2.username}')

        # Create or get conversation
        conversation, created = Conversation.objects.get_or_create(
            defaults={}
        )
        if created:
            conversation.participants.add(user1, user2)
            self.stdout.write(f'Created conversation: {conversation.id}')

        # Create a test message
        message = Message.objects.create(
            conversation=conversation,
            sender=user1,
            content='Hello! This is a test message.'
        )
        self.stdout.write(f'Created message: {message.id}')

        # Create notification
        notification = Notification.objects.create(
            recipient=user2,
            sender=user1,
            notification_type='message',
            conversation=conversation,
            message=f"{user1.username} sent you a message."
        )
        self.stdout.write(f'Created notification: {notification.id}')

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created test message notification!\n'
                f'Login as {user2.username} (password: testpass123) to see the notification.'
            )
        )
