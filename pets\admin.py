from django.contrib import admin
from .models import PetCategory, PetBreed, Pet, PetGallery, PetMedicalRecord


class PetBreedInline(admin.TabularInline):
    model = PetBreed
    extra = 1


@admin.register(PetCategory)
class PetCategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'description')
    inlines = [PetBreedInline]


@admin.register(PetBreed)
class PetBreedAdmin(admin.ModelAdmin):
    list_display = ('name', 'category', 'average_lifespan', 'size')
    list_filter = ('category',)
    search_fields = ('name', 'description')


class PetGalleryInline(admin.TabularInline):
    model = PetGallery
    extra = 1


class PetMedicalRecordInline(admin.TabularInline):
    model = PetMedicalRecord
    extra = 0


@admin.register(Pet)
class PetAdmin(admin.ModelAdmin):
    list_display = ('name', 'owner', 'category', 'breed', 'gender', 'is_for_adoption')
    list_filter = ('category', 'gender', 'is_for_adoption')
    search_fields = ('name', 'owner__username', 'breed__name')
    inlines = [PetGalleryInline, PetMedicalRecordInline]


@admin.register(PetGallery)
class PetGalleryAdmin(admin.ModelAdmin):
    list_display = ('pet', 'caption', 'created_at')
    list_filter = ('pet__category',)
    search_fields = ('pet__name', 'caption')


@admin.register(PetMedicalRecord)
class PetMedicalRecordAdmin(admin.ModelAdmin):
    list_display = ('pet', 'record_date', 'record_type', 'veterinarian')
    list_filter = ('record_type', 'record_date')
    search_fields = ('pet__name', 'description', 'veterinarian', 'clinic')
